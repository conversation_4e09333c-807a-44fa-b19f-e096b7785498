"""
Tests for data models
"""

import pytest

from django_n1_guard.models import (
    Ser<PERSON><PERSON><PERSON>ield, QuerySetUsage, N1Issue, AnalysisResult,
    SerializerUsage, ModelField
)


class TestSerializerField:
    """Test SerializerField model"""
    
    def test_requires_select_related(self):
        """Test requires_select_related property"""
        # Single relation field should require select_related
        field = Serial<PERSON><PERSON>ield(
            name="author",
            field_type="Char<PERSON>ield", 
            is_relation=True,
            is_many=False
        )
        assert field.requires_select_related
        assert not field.requires_prefetch_related
        
        # Many relation field should not require select_related
        many_field = SerializerField(
            name="reviews",
            field_type="PrimaryKeyRelatedField",
            is_relation=True,
            is_many=True
        )
        assert not many_field.requires_select_related
        assert many_field.requires_prefetch_related
        
        # Non-relation field should not require either
        non_relation = Serial<PERSON><PERSON>ield(
            name="title",
            field_type="Char<PERSON>ield",
            is_relation=False
        )
        assert not non_relation.requires_select_related
        assert not non_relation.requires_prefetch_related
    
    def test_field_path_property(self):
        """Test field_path property"""
        # Field without source should use name
        field = Serial<PERSON><PERSON>ield(
            name="author_name",
            field_type="<PERSON><PERSON><PERSON><PERSON>",
            is_relation=True
        )
        assert field.field_path == "author_name"
        
        # Field with source should use source
        field_with_source = SerializerField(
            name="author_name",
            field_type="CharField", 
            is_relation=True,
            source="author.name"
        )
        assert field_with_source.field_path == "author.name"


class TestQuerySetUsage:
    """Test QuerySetUsage model"""
    
    def test_has_optimization_for_select_related(self):
        """Test optimization detection for select_related fields"""
        queryset = QuerySetUsage(
            model_name="Book",
            view_name="BookViewSet",
            select_related_fields={"author", "publisher"}
        )
        
        # Field that requires select_related and is optimized
        optimized_field = SerializerField(
            name="author_name",
            field_type="CharField",
            is_relation=True,
            is_many=False,
            source="author.name"
        )
        assert queryset.has_optimization_for(optimized_field)
        
        # Field that requires select_related but is not optimized
        unoptimized_field = SerializerField(
            name="category_name", 
            field_type="CharField",
            is_relation=True,
            is_many=False,
            source="category.name"
        )
        assert not queryset.has_optimization_for(unoptimized_field)
    
    def test_has_optimization_for_prefetch_related(self):
        """Test optimization detection for prefetch_related fields"""
        queryset = QuerySetUsage(
            model_name="Book",
            view_name="BookViewSet", 
            prefetch_related_fields={"reviews", "tags"}
        )
        
        # Field that requires prefetch_related and is optimized
        optimized_field = SerializerField(
            name="reviews",
            field_type="PrimaryKeyRelatedField",
            is_relation=True,
            is_many=True
        )
        assert queryset.has_optimization_for(optimized_field)
        
        # Field that requires prefetch_related but is not optimized
        unoptimized_field = SerializerField(
            name="comments",
            field_type="PrimaryKeyRelatedField", 
            is_relation=True,
            is_many=True
        )
        assert not queryset.has_optimization_for(unoptimized_field)
    
    def test_prefetch_objects_optimization(self):
        """Test optimization detection with Prefetch objects"""
        queryset = QuerySetUsage(
            model_name="Book",
            view_name="BookViewSet",
            prefetch_objects=[
                {"lookup": "reviews", "queryset": "Review.objects.select_related('user')"},
                {"lookup": "tags", "to_attr": "cached_tags"}
            ]
        )
        
        # Field optimized with Prefetch object
        prefetch_field = SerializerField(
            name="reviews",
            field_type="ReviewSerializer",
            is_relation=True,
            is_many=True
        )
        assert queryset.has_optimization_for(prefetch_field)
        
        # Field not in Prefetch objects
        unoptimized_field = SerializerField(
            name="comments",
            field_type="CommentSerializer",
            is_relation=True, 
            is_many=True
        )
        assert not queryset.has_optimization_for(unoptimized_field)


class TestN1Issue:
    """Test N1Issue model"""
    
    def test_unique_id_generation(self):
        """Test unique ID generation"""
        issue = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="author",
            field_path="author",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10
        )
        
        expected_id = "/app/serializers.py:10:author"
        assert issue.unique_id == expected_id
    
    def test_format_suggestion(self):
        """Test suggestion formatting"""
        # select_related issue
        select_issue = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet", 
            field_name="author",
            field_path="author",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10
        )
        
        suggestion = select_issue.format_suggestion()
        assert suggestion == ".select_related('author')"
        
        # prefetch_related issue
        prefetch_issue = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="reviews", 
            field_path="reviews",
            issue_type="prefetch_related",
            file_path="/app/serializers.py",
            line_number=15
        )
        
        suggestion = prefetch_issue.format_suggestion()
        assert suggestion == ".prefetch_related('reviews')"
    
    def test_hash_and_equality(self):
        """Test hash and equality methods"""
        issue1 = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="author",
            field_path="author", 
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10
        )
        
        issue2 = N1Issue(
            serializer_name="BookSerializer", 
            view_name="BookViewSet",
            field_name="author",
            field_path="author",
            issue_type="select_related", 
            file_path="/app/serializers.py",
            line_number=10
        )
        
        issue3 = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet", 
            field_name="reviews",  # Different field
            field_path="reviews",
            issue_type="prefetch_related",
            file_path="/app/serializers.py",
            line_number=15
        )
        
        # Same issues should be equal and have same hash
        assert issue1 == issue2
        assert hash(issue1) == hash(issue2)
        
        # Different issues should not be equal
        assert issue1 != issue3
        assert hash(issue1) != hash(issue3)
        
        # Should be able to use in sets
        issue_set = {issue1, issue2, issue3}
        assert len(issue_set) == 2  # issue1 and issue2 are duplicates


class TestAnalysisResult:
    """Test AnalysisResult model"""
    
    def test_total_issues_property(self):
        """Test total_issues property"""
        issues = [
            N1Issue(
                serializer_name="BookSerializer",
                view_name="BookViewSet",
                field_name="author", 
                field_path="author",
                issue_type="select_related",
                file_path="/app/serializers.py",
                line_number=10,
                severity="info"
            ),
            N1Issue(
                serializer_name="BookSerializer",
                view_name="BookViewSet", 
                field_name="reviews",
                field_path="reviews",
                issue_type="prefetch_related",
                file_path="/app/serializers.py", 
                line_number=15,
                severity="warning"
            )
        ]
        
        result = AnalysisResult(issues=issues)
        assert result.total_issues == 2
    
    def test_issues_by_severity(self):
        """Test issues_by_severity property"""
        issues = [
            N1Issue(
                serializer_name="BookSerializer",
                view_name="BookViewSet", 
                field_name="author",
                field_path="author",
                issue_type="select_related",
                file_path="/app/serializers.py",
                line_number=10,
                severity="info"
            ),
            N1Issue(
                serializer_name="BookSerializer",
                view_name="BookViewSet",
                field_name="reviews",
                field_path="reviews", 
                issue_type="prefetch_related",
                file_path="/app/serializers.py",
                line_number=15,
                severity="warning"
            ),
            N1Issue(
                serializer_name="AuthorSerializer",
                view_name="AuthorViewSet",
                field_name="books",
                field_path="books",
                issue_type="prefetch_related", 
                file_path="/app/views.py",
                line_number=25,
                severity="error"
            )
        ]
        
        result = AnalysisResult(issues=issues)
        by_severity = result.issues_by_severity
        
        assert len(by_severity['info']) == 1
        assert len(by_severity['warning']) == 1
        assert len(by_severity['error']) == 1
        assert by_severity['info'][0].field_name == "author"
        assert by_severity['warning'][0].field_name == "reviews"
        assert by_severity['error'][0].field_name == "books"
    
    def test_issues_by_file(self):
        """Test issues_by_file property"""
        issues = [
            N1Issue(
                serializer_name="BookSerializer",
                view_name="BookViewSet",
                field_name="author",
                field_path="author", 
                issue_type="select_related",
                file_path="/app/serializers.py",
                line_number=10
            ),
            N1Issue(
                serializer_name="BookSerializer", 
                view_name="BookViewSet",
                field_name="reviews",
                field_path="reviews",
                issue_type="prefetch_related",
                file_path="/app/serializers.py",
                line_number=15
            ),
            N1Issue(
                serializer_name="AuthorSerializer",
                view_name="AuthorViewSet", 
                field_name="books",
                field_path="books",
                issue_type="prefetch_related",
                file_path="/app/views.py",
                line_number=25
            )
        ]
        
        result = AnalysisResult(issues=issues)
        by_file = result.issues_by_file
        
        assert len(by_file["/app/serializers.py"]) == 2
        assert len(by_file["/app/views.py"]) == 1
        
        serializer_issues = by_file["/app/serializers.py"]
        view_issues = by_file["/app/views.py"]
        
        assert {i.field_name for i in serializer_issues} == {"author", "reviews"}
        assert view_issues[0].field_name == "books"


class TestModelField:
    """Test ModelField model"""
    
    def test_relation_field_types(self):
        """Test different relation field types"""
        # ForeignKey field
        fk_field = ModelField(
            name="author",
            field_type="ForeignKey",
            is_relation=True,
            related_model="Author",
            is_foreign_key=True
        )
        assert fk_field.is_foreign_key
        assert not fk_field.is_many_to_many
        assert not fk_field.is_one_to_one
        
        # ManyToManyField
        m2m_field = ModelField(
            name="tags",
            field_type="ManyToManyField", 
            is_relation=True,
            related_model="Tag",
            is_many_to_many=True
        )
        assert m2m_field.is_many_to_many
        assert not m2m_field.is_foreign_key
        assert not m2m_field.is_one_to_one
        
        # OneToOneField
        o2o_field = ModelField(
            name="profile",
            field_type="OneToOneField",
            is_relation=True, 
            related_model="Profile",
            is_one_to_one=True
        )
        assert o2o_field.is_one_to_one
        assert not o2o_field.is_foreign_key
        assert not o2o_field.is_many_to_many


class TestSerializerUsage:
    """Test SerializerUsage model"""
    
    def test_different_usage_types(self):
        """Test different serializer usage types"""
        # Class attribute usage
        class_usage = SerializerUsage(
            view_name="BookViewSet",
            serializer_name="BookSerializer", 
            usage_type="class_attr",
            line_number=10
        )
        assert class_usage.usage_type == "class_attr"
        assert class_usage.action_name is None
        
        # get_serializer_class method usage
        method_usage = SerializerUsage(
            view_name="BookViewSet",
            serializer_name="BookSerializer",
            usage_type="get_serializer_class", 
            line_number=20
        )
        assert method_usage.usage_type == "get_serializer_class"
        
        # Action method usage
        action_usage = SerializerUsage(
            view_name="BookViewSet", 
            serializer_name="BookDetailSerializer",
            usage_type="action",
            action_name="detail_view",
            line_number=30
        )
        assert action_usage.usage_type == "action"
        assert action_usage.action_name == "detail_view"