"""
Tests for the N+1 detector module
"""

import tempfile
from pathlib import Path
import pytest

from django_n1_guard.detector import N1Detector
from django_n1_guard.models import N1Issue


class TestN1Detector:
    """Test the N+1 detector"""
    
    def test_simple_n1_detection(self):
        """Test basic N+1 issue detection"""
        serializer_code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'reviews']
"""
        
        viewset_code = """
from rest_framework.viewsets import ModelViewSet

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()  # No optimizations!
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            # Write files
            (tmp_path / "serializers.py").write_text(serializer_code)
            (tmp_path / "views.py").write_text(viewset_code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Should detect N+1 issues
            assert len(result.issues) >= 2
            
            # Check for select_related issue
            select_issue = next((i for i in result.issues if i.issue_type == 'select_related'), None)
            assert select_issue is not None
            assert select_issue.field_name == 'author_name'
            
            # Check for prefetch_related issue  
            prefetch_issue = next((i for i in result.issues if i.issue_type == 'prefetch_related'), None)
            assert prefetch_issue is not None
            assert prefetch_issue.field_name == 'reviews'
    
    def test_optimized_viewset_no_issues(self):
        """Test that optimized viewsets don't generate issues"""
        serializer_code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'reviews']
"""
        
        viewset_code = """
from rest_framework.viewsets import ModelViewSet

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    
    def get_queryset(self):
        return Book.objects.select_related('author').prefetch_related('reviews')
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            (tmp_path / "serializers.py").write_text(serializer_code)
            (tmp_path / "views.py").write_text(viewset_code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Should not detect any issues
            assert len(result.issues) == 0
    
    def test_serializer_method_field_detection(self):
        """Test SerializerMethodField N+1 detection"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    review_count = serializers.SerializerMethodField()
    
    def get_review_count(self, obj):
        return obj.reviews.count()  # N+1 issue!
    
    class Meta:
        model = Book
        fields = ['title', 'review_count']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()  # No prefetch_related!
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            (tmp_path / "views.py").write_text(code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Should detect prefetch_related issue for SerializerMethodField
            assert len(result.issues) >= 1
            
            method_field_issue = next(
                (i for i in result.issues 
                 if i.field_name == 'review_count' and i.issue_type == 'prefetch_related'), 
                None
            )
            assert method_field_issue is not None
            assert method_field_issue.severity in ['warning', 'error']
    
    def test_nested_serializer_detection(self):
        """Test nested serializer N+1 detection"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class AuthorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Author
        fields = ['name', 'email']

class BookSerializer(serializers.ModelSerializer):
    author = AuthorSerializer(read_only=True)
    
    class Meta:
        model = Book
        fields = ['title', 'author']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()  # Missing select_related!
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            (tmp_path / "views.py").write_text(code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Should detect select_related issue for nested serializer
            assert len(result.issues) >= 1
            
            nested_issue = next(
                (i for i in result.issues 
                 if i.field_name == 'author' and i.issue_type == 'select_related'),
                None
            )
            assert nested_issue is not None
    
    def test_confidence_scoring(self):
        """Test confidence scoring for issues"""
        # SerializerMethodField should have lower confidence
        method_field_code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    review_count = serializers.SerializerMethodField()
    
    def get_review_count(self, obj):
        return obj.reviews.count()
    
    class Meta:
        model = Book
        fields = ['title', 'review_count']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        # Direct field should have higher confidence
        direct_field_code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    
    class Meta:
        model = Book
        fields = ['title', 'author_name']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            (tmp_path / "method_fields.py").write_text(method_field_code)
            (tmp_path / "direct_fields.py").write_text(direct_field_code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            method_issue = next(
                (i for i in result.issues if 'method_fields.py' in i.file_path),
                None
            )
            direct_issue = next(
                (i for i in result.issues if 'direct_fields.py' in i.file_path),
                None
            )
            
            assert method_issue is not None
            assert direct_issue is not None
            
            # Direct field should have higher confidence than method field
            assert direct_issue.confidence > method_issue.confidence
    
    def test_severity_levels(self):
        """Test severity level assignment"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    # Simple foreign key - should be 'info'
    author_name = serializers.CharField(source='author.name')
    
    # Many relationship - should be 'warning'
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    
    # SerializerMethodField with many - should be 'error'
    review_summaries = serializers.SerializerMethodField()
    
    def get_review_summaries(self, obj):
        return [r.summary for r in obj.reviews.all()]
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'reviews', 'review_summaries']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            (tmp_path / "test.py").write_text(code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Check severity levels
            author_issue = next((i for i in result.issues if i.field_name == 'author_name'), None)
            reviews_issue = next((i for i in result.issues if i.field_name == 'reviews'), None)
            method_issue = next((i for i in result.issues if i.field_name == 'review_summaries'), None)
            
            assert author_issue is not None
            assert reviews_issue is not None  
            assert method_issue is not None
            
            # Check severity progression
            assert author_issue.severity == 'info'
            assert reviews_issue.severity == 'warning'
            assert method_issue.severity == 'error'
    
    def test_exclude_patterns(self):
        """Test exclusion patterns"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            # Create files that should be excluded
            migrations_dir = tmp_path / "migrations"
            migrations_dir.mkdir()
            (migrations_dir / "0001_initial.py").write_text(code)
            
            # Create file that should be included
            (tmp_path / "serializers.py").write_text(code)
            
            detector = N1Detector(exclude_patterns=['migrations'])
            result = detector.analyze_directory(tmp_path)
            
            # Should only analyze serializers.py, not migration file
            analyzed_files = {issue.file_path for issue in result.issues}
            assert any('serializers.py' in f for f in analyzed_files)
            assert not any('migrations' in f for f in analyzed_files)
    
    def test_statistics(self):
        """Test analysis statistics"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            (tmp_path / "test.py").write_text(code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            stats = detector.get_statistics()
            
            assert stats['total_issues'] == len(result.issues)
            assert stats['select_related_issues'] + stats['prefetch_related_issues'] == stats['total_issues']
            assert 'high_confidence_issues' in stats
            assert 'error_severity' in stats
            assert 'warning_severity' in stats
            assert 'info_severity' in stats
    
    def test_cross_file_analysis(self):
        """Test cross-file analysis capabilities"""
        serializer_code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    
    class Meta:
        model = Book
        fields = ['title', 'author_name']
"""
        
        viewset_code = """
from rest_framework.viewsets import ModelViewSet
from .serializers import BookSerializer

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            (tmp_path / "serializers.py").write_text(serializer_code)
            (tmp_path / "views.py").write_text(viewset_code)
            
            detector = N1Detector()
            result = detector.analyze_directory(tmp_path)
            
            # Should detect cross-file N+1 issue
            assert len(result.issues) >= 1
            
            cross_file_issue = next(
                (i for i in result.issues if i.serializer_name == 'BookSerializer'),
                None
            )
            assert cross_file_issue is not None
            assert cross_file_issue.view_name == 'BookViewSet'