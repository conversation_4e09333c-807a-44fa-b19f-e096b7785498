"""
Tests for the CLI module
"""

import json
import tempfile
from pathlib import Path

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from django_n1_guard.cli import main


class TestCLI:
    """Test the command line interface"""
    
    def test_analyze_empty_directory(self):
        """Test analyzing empty directory"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [tmpdir])
            
            assert result.exit_code == 0
            assert "No N+1 query issues detected" in result.output
    
    def test_analyze_file_with_issues(self):
        """Test analyzing file with N+1 issues"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    
    class Meta:
        model = Book
        fields = ['title', 'author_name']

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "test.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file)])
            
            assert result.exit_code == 0
            assert "N+1 Query Issues Detected" in result.output
            assert "BookSerializer" in result.output
            assert "author_name" in result.output
    
    def test_json_output_format(self):
        """Test JSON output format"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "test.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file), '--format', 'json'])
            
            assert result.exit_code == 0
            
            # Should be valid JSON
            output_data = json.loads(result.output)
            assert 'summary' in output_data
            assert 'issues' in output_data
            assert output_data['summary']['total_issues'] >= 1
    
    def test_github_output_format(self):
        """Test GitHub Actions output format"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "test.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file), '--format', 'github'])
            
            assert result.exit_code == 0
            
            # Should contain GitHub Actions annotation format
            assert '::' in result.output
            assert 'file=' in result.output
            assert 'line=' in result.output
    
    def test_fail_on_issues_flag(self):
        """Test --fail-on-issues flag"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "test.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file), '--fail-on-issues'])
            
            # Should exit with error code when issues found
            assert result.exit_code == 1
    
    def test_exclude_patterns(self):
        """Test exclude patterns functionality"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            # Create file that should be excluded
            migrations_dir = tmp_path / "migrations"
            migrations_dir.mkdir()
            (migrations_dir / "0001_initial.py").write_text(code)
            
            # Create file that should be included
            (tmp_path / "serializers.py").write_text(code)
            
            result = runner.invoke(main, [
                str(tmp_path), 
                '--exclude', 'migrations',
                '--format', 'json'
            ])
            
            assert result.exit_code == 0
            
            # Parse output to check which files were analyzed
            output_data = json.loads(result.output)
            analyzed_files = {issue['file'] for issue in output_data['issues']}
            
            # Should not analyze migration files
            assert not any('migrations' in f for f in analyzed_files)
            assert any('serializers.py' in f for f in analyzed_files)
    
    def test_min_confidence_filter(self):
        """Test minimum confidence filtering"""
        # This would require creating issues with different confidence levels
        # For now, just test that the option is accepted
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [tmpdir, '--min-confidence', '0.8'])
            assert result.exit_code == 0
    
    def test_severity_filter(self):
        """Test severity filtering"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [
                tmpdir, 
                '--severity', 'error',
                '--severity', 'warning'
            ])
            assert result.exit_code == 0
    
    def test_stats_option(self):
        """Test --stats option"""
        code = """
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "test.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file), '--stats'])
            
            assert result.exit_code == 0
            assert "📊 Analysis Statistics:" in result.output
            assert "Files analyzed:" in result.output
            assert "Total lines:" in result.output
    
    def test_quiet_option(self):
        """Test --quiet option"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [tmpdir, '--quiet'])
            
            assert result.exit_code == 0
            # Should not contain progress messages
            assert "Django N+1 Guard" not in result.output
            assert "Analyzing:" not in result.output
    
    def test_verbose_option(self):
        """Test --verbose option"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [tmpdir, '--verbose'])
            
            assert result.exit_code == 0
            # Should accept the option without error
    
    def test_nonexistent_path(self):
        """Test error handling for nonexistent path"""
        runner = CliRunner()
        
        result = runner.invoke(main, ['/nonexistent/path'])
        
        assert result.exit_code == 2  # Click's error code for bad parameter
    
    def test_config_file_json(self):
        """Test JSON configuration file"""
        config = {
            "exclude": ["test_*", "migrations"],
            "min_confidence": 0.8,
            "severity": ["error", "warning"]
        }
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            config_file = tmp_path / "config.json"
            config_file.write_text(json.dumps(config))
            
            result = runner.invoke(main, [str(tmp_path), '--config', str(config_file)])
            
            assert result.exit_code == 0
    
    def test_config_file_yaml(self):
        """Test YAML configuration file (if PyYAML available)"""
        pytest.importorskip("yaml")  # Skip if PyYAML not available
        
        config_content = """
exclude:
  - test_*
  - migrations
min_confidence: 0.8
severity:
  - error
  - warning
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            config_file = tmp_path / "config.yml"
            config_file.write_text(config_content)
            
            result = runner.invoke(main, [str(tmp_path), '--config', str(config_file)])
            
            assert result.exit_code == 0
    
    def test_invalid_config_file(self):
        """Test handling of invalid configuration file"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            config_file = tmp_path / "config.json"
            config_file.write_text("invalid json content")
            
            result = runner.invoke(main, [str(tmp_path), '--config', str(config_file)])
            
            # Should continue with default config and show warning
            assert result.exit_code == 0
            assert "Error loading config" in result.output
    
    def test_version_option(self):
        """Test --version option"""
        runner = CliRunner()
        
        result = runner.invoke(main, ['--version'])
        
        assert result.exit_code == 0
        assert "0.1.0" in result.output


class TestCLIEdgeCases:
    """Test edge cases for CLI"""
    
    def test_large_codebase_simulation(self):
        """Test handling of large codebase (simulated)"""
        # Create multiple files with issues
        files_content = []
        for i in range(10):
            content = f"""
from rest_framework import serializers
from rest_framework.viewsets import ModelViewSet

class Serializer{i}(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')

class ViewSet{i}(ModelViewSet):
    serializer_class = Serializer{i}
    queryset = Model{i}.objects.all()
"""
            files_content.append((f"module{i}.py", content))
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            
            for filename, content in files_content:
                (tmp_path / filename).write_text(content)
            
            result = runner.invoke(main, [str(tmp_path), '--format', 'json'])
            
            assert result.exit_code == 0
            
            # Should handle multiple files
            output_data = json.loads(result.output)
            assert output_data['summary']['files_analyzed'] == 10
            assert output_data['summary']['total_issues'] >= 10
    
    def test_empty_python_file(self):
        """Test handling of empty Python file"""
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            empty_file = tmp_path / "empty.py"
            empty_file.write_text("")
            
            result = runner.invoke(main, [str(empty_file)])
            
            assert result.exit_code == 0
            assert "No N+1 query issues detected" in result.output
    
    def test_syntax_error_in_file(self):
        """Test handling of Python file with syntax errors"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer:  # Missing closing paren
    author_name = serializers.CharField(source='author.name')
"""
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = Path(tmpdir)
            test_file = tmp_path / "syntax_error.py"
            test_file.write_text(code)
            
            result = runner.invoke(main, [str(test_file)])
            
            # Should handle syntax errors gracefully
            assert result.exit_code == 0
    
    def test_keyboard_interrupt_simulation(self):
        """Test keyboard interrupt handling (simulated)"""
        # This is difficult to test directly, but we can test the error handling structure
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            result = runner.invoke(main, [tmpdir])
            
            # Should complete normally
            assert result.exit_code == 0