"""
Pytest configuration and shared fixtures
"""

import tempfile
from pathlib import Path
from typing import Generator

import pytest

from django_n1_guard.detector import N1Detector
from django_n1_guard.models import N1Issue, AnalysisResult


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for testing"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_serializer_code() -> str:
    """Sample serializer code with N+1 issues"""
    return """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.Char<PERSON>ield(source='author.name')
    publisher_name = serializers.CharField(source='publisher.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    review_count = serializers.SerializerMethodField()
    
    def get_review_count(self, obj):
        return obj.reviews.count()
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'publisher_name', 'reviews', 'tags', 'review_count']

class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['name', 'color']
"""


@pytest.fixture 
def sample_viewset_code() -> str:
    """Sample viewset code without optimizations"""
    return """
from rest_framework.viewsets import ModelViewSet
from .serializers import BookSerializer

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()  # No optimizations!
    
    def get_queryset(self):
        # Still no optimizations
        return Book.objects.filter(published=True)
"""


@pytest.fixture
def optimized_viewset_code() -> str:
    """Sample viewset code with proper optimizations"""
    return """
from rest_framework.viewsets import ModelViewSet
from .serializers import BookSerializer

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    
    def get_queryset(self):
        return Book.objects.select_related('author', 'publisher') \\
                          .prefetch_related('reviews', 'tags')
"""


@pytest.fixture
def sample_model_code() -> str:
    """Sample model code"""
    return """
from django.db import models

class Author(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()

class Publisher(models.Model):
    name = models.CharField(max_length=100)
    address = models.ForeignKey('Address', on_delete=models.CASCADE)

class Book(models.Model):
    title = models.CharField(max_length=200)
    author = models.ForeignKey(Author, on_delete=models.CASCADE)
    publisher = models.ForeignKey(Publisher, on_delete=models.CASCADE, related_name='books')
    tags = models.ManyToManyField('Tag', blank=True)
    published = models.BooleanField(default=False)

class Tag(models.Model):
    name = models.CharField(max_length=50)
    color = models.CharField(max_length=7)

class Review(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey('User', on_delete=models.CASCADE)
    rating = models.IntegerField()
    summary = models.TextField()
"""


@pytest.fixture
def sample_n1_issues() -> list[N1Issue]:
    """Create sample N+1 issues for testing"""
    return [
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="author_name",
            field_path="author.name",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=5,
            severity="info",
            confidence=0.9,
            suggestion=".select_related('author')"
        ),
        N1Issue(
            serializer_name="BookSerializer", 
            view_name="BookViewSet",
            field_name="publisher_name",
            field_path="publisher.name",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=6,
            severity="info",
            confidence=0.9,
            suggestion=".select_related('publisher')"
        ),
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="reviews",
            field_path="reviews", 
            issue_type="prefetch_related",
            file_path="/app/serializers.py",
            line_number=7,
            severity="warning",
            confidence=0.8,
            suggestion=".prefetch_related('reviews')"
        ),
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="tags",
            field_path="tags",
            issue_type="prefetch_related", 
            file_path="/app/serializers.py",
            line_number=8,
            severity="warning",
            confidence=0.8,
            suggestion=".prefetch_related('tags')"
        ),
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="review_count",
            field_path="reviews",
            issue_type="prefetch_related",
            file_path="/app/serializers.py", 
            line_number=11,
            severity="error",
            confidence=0.7,
            suggestion=".prefetch_related('reviews')"
        )
    ]


@pytest.fixture
def sample_analysis_result(sample_n1_issues) -> AnalysisResult:
    """Create sample analysis result"""
    return AnalysisResult(
        issues=sample_n1_issues,
        files_analyzed=3,
        total_lines=150,
        analysis_time=0.5
    )


@pytest.fixture
def detector() -> N1Detector:
    """Create a detector instance"""
    return N1Detector()


@pytest.fixture
def project_with_issues(temp_dir, sample_serializer_code, sample_viewset_code, sample_model_code) -> Path:
    """Create a temporary project with N+1 issues"""
    (temp_dir / "models.py").write_text(sample_model_code)
    (temp_dir / "serializers.py").write_text(sample_serializer_code)
    (temp_dir / "views.py").write_text(sample_viewset_code)
    (temp_dir / "__init__.py").write_text("")
    
    return temp_dir


@pytest.fixture
def project_without_issues(temp_dir, sample_serializer_code, optimized_viewset_code, sample_model_code) -> Path:
    """Create a temporary project without N+1 issues"""
    (temp_dir / "models.py").write_text(sample_model_code)
    (temp_dir / "serializers.py").write_text(sample_serializer_code)
    (temp_dir / "views.py").write_text(optimized_viewset_code)
    (temp_dir / "__init__.py").write_text("")
    
    return temp_dir


@pytest.fixture
def complex_project(temp_dir) -> Path:
    """Create a more complex project structure"""
    # Create app directories
    app1_dir = temp_dir / "app1"
    app2_dir = temp_dir / "app2"
    migrations_dir = app1_dir / "migrations"
    
    app1_dir.mkdir()
    app2_dir.mkdir()
    migrations_dir.mkdir()
    
    # App1 with issues
    (app1_dir / "__init__.py").write_text("")
    (app1_dir / "models.py").write_text("""
from django.db import models

class User(models.Model):
    name = models.CharField(max_length=100)
    profile = models.OneToOneField('Profile', on_delete=models.CASCADE)
""")
    
    (app1_dir / "serializers.py").write_text("""
from rest_framework import serializers

class UserSerializer(serializers.ModelSerializer):
    profile_bio = serializers.CharField(source='profile.bio')
    
    class Meta:
        model = User
        fields = ['name', 'profile_bio']
""")
    
    (app1_dir / "views.py").write_text("""
from rest_framework.viewsets import ModelViewSet

class UserViewSet(ModelViewSet):
    serializer_class = UserSerializer
    queryset = User.objects.all()
""")
    
    # App2 without issues  
    (app2_dir / "__init__.py").write_text("")
    (app2_dir / "serializers.py").write_text("""
from rest_framework import serializers

class SimpleSerializer(serializers.Serializer):
    name = serializers.CharField()
""")
    
    # Migration file (should be excluded)
    (migrations_dir / "__init__.py").write_text("")
    (migrations_dir / "0001_initial.py").write_text("""
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = []
    operations = []
""")
    
    return temp_dir


# Test data constants
DJANGO_IMPORTS = [
    "from django.db import models",
    "from django.contrib.auth.models import User",
    "import django.db.models.fields",
]

DRF_IMPORTS = [
    "from rest_framework import serializers",
    "from rest_framework.viewsets import ModelViewSet",
    "from rest_framework.views import APIView",
    "import rest_framework.serializers",
]

SERIALIZER_FIELD_TYPES = [
    "CharField", "IntegerField", "BooleanField",
    "PrimaryKeyRelatedField", "StringRelatedField", 
    "SlugRelatedField", "HyperlinkedRelatedField",
    "SerializerMethodField"
]

MODEL_FIELD_TYPES = [
    "CharField", "IntegerField", "BooleanField",
    "ForeignKey", "OneToOneField", "ManyToManyField",
    "DateTimeField", "EmailField"
]