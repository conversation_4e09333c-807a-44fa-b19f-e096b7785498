"""
Tests for the AST analyzer module
"""

import ast
import pytest

from django_n1_guard.analyzer import DjangoASTAnalyzer
from django_n1_guard.models import Serial<PERSON><PERSON>ield, QuerySetUsage, ModelField


class TestDjangoASTAnalyzer:
    """Test the Django AST analyzer"""
    
    def test_serializer_detection(self):
        """Test basic serializer detection"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    reviews = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'reviews']
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "BookSerializer" in analyzer.serializers
        fields = analyzer.serializers["BookSerializer"]
        
        # Find the relation fields
        author_field = next((f for f in fields if f.name == 'author_name'), None)
        reviews_field = next((f for f in fields if f.name == 'reviews'), None)
        
        assert author_field is not None
        assert author_field.is_relation
        assert not author_field.is_many
        assert author_field.source == 'author.name'
        
        assert reviews_field is not None  
        assert reviews_field.is_relation
        assert reviews_field.is_many
    
    def test_viewset_detection(self):
        """Test ViewSet detection and QuerySet analysis"""
        code = """
from rest_framework.viewsets import ModelViewSet
from django.db import models

class BookViewSet(ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.select_related('author').prefetch_related('reviews')
    
    def get_queryset(self):
        return Book.objects.select_related('author', 'publisher') \\
                          .prefetch_related('reviews', 'tags')
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "BookViewSet" in analyzer.viewsets
        querysets = analyzer.viewsets["BookViewSet"]
        assert len(querysets) >= 1
        
        # Check the class-level queryset
        class_qs = next((qs for qs in querysets if not qs.method_name), None)
        if class_qs:
            assert class_qs.has_select_related
            assert class_qs.has_prefetch_related
            assert 'author' in class_qs.select_related_fields
            assert 'reviews' in class_qs.prefetch_related_fields
        
        # Check the get_queryset method
        method_qs = next((qs for qs in querysets if qs.method_name == 'get_queryset'), None)
        if method_qs:
            assert method_qs.has_select_related
            assert method_qs.has_prefetch_related
            assert 'author' in method_qs.select_related_fields
            assert 'publisher' in method_qs.select_related_fields
            assert 'reviews' in method_qs.prefetch_related_fields
            assert 'tags' in method_qs.prefetch_related_fields
    
    def test_model_field_detection(self):
        """Test Django model field detection"""
        code = """
from django.db import models

class Book(models.Model):
    title = models.CharField(max_length=200)
    author = models.ForeignKey('Author', on_delete=models.CASCADE)
    publisher = models.ForeignKey('Publisher', on_delete=models.CASCADE, related_name='books')
    tags = models.ManyToManyField('Tag', blank=True)
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "Book" in analyzer.models
        fields = analyzer.models["Book"]
        
        # Find relation fields
        author_field = next((f for f in fields if f.name == 'author'), None)
        publisher_field = next((f for f in fields if f.name == 'publisher'), None)
        tags_field = next((f for f in fields if f.name == 'tags'), None)
        
        assert author_field is not None
        assert author_field.is_relation
        assert author_field.is_foreign_key
        assert not author_field.is_many_to_many
        
        assert publisher_field is not None
        assert publisher_field.is_relation
        assert publisher_field.related_name == 'books'
        
        assert tags_field is not None
        assert tags_field.is_relation
        assert tags_field.is_many_to_many
    
    def test_serializer_method_field(self):
        """Test SerializerMethodField detection"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    review_count = serializers.SerializerMethodField()
    top_reviews = serializers.SerializerMethodField()
    
    def get_review_count(self, obj):
        return obj.reviews.count()
    
    def get_top_reviews(self, obj):
        return obj.reviews.filter(rating__gte=4)
    
    class Meta:
        model = Book
        fields = ['title', 'review_count', 'top_reviews']
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "BookSerializer" in analyzer.serializers
        fields = analyzer.serializers["BookSerializer"]
        
        # Check method fields
        review_count = next((f for f in fields if f.name == 'review_count'), None)
        top_reviews = next((f for f in fields if f.name == 'top_reviews'), None)
        
        assert review_count is not None
        assert 'SerializerMethodField' in review_count.field_type
        assert review_count.is_relation
        assert review_count.is_many  # .count() suggests many
        
        assert top_reviews is not None
        assert 'SerializerMethodField' in top_reviews.field_type
        assert top_reviews.is_relation
        assert top_reviews.is_many  # .filter() suggests many
    
    def test_nested_serializer(self):
        """Test nested serializer detection"""
        code = """
from rest_framework import serializers

class AuthorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Author
        fields = ['name', 'email']

class BookSerializer(serializers.ModelSerializer):
    author = AuthorSerializer(read_only=True)
    tags = TagSerializer(many=True)
    
    class Meta:
        model = Book
        fields = ['title', 'author', 'tags']
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "BookSerializer" in analyzer.serializers
        fields = analyzer.serializers["BookSerializer"]
        
        author_field = next((f for f in fields if f.name == 'author'), None)
        tags_field = next((f for f in fields if f.name == 'tags'), None)
        
        assert author_field is not None
        assert author_field.is_relation
        assert not author_field.is_many
        assert 'Serializer' in author_field.field_type
        
        assert tags_field is not None
        assert tags_field.is_relation
        assert tags_field.is_many
    
    def test_prefetch_object_detection(self):
        """Test Prefetch object detection"""
        code = """
from django.db.models import Prefetch
from rest_framework.viewsets import ModelViewSet

class BookViewSet(ModelViewSet):
    def get_queryset(self):
        return Book.objects.prefetch_related(
            Prefetch('reviews', queryset=Review.objects.select_related('user')),
            Prefetch('tags', to_attr='cached_tags')
        )
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        assert "BookViewSet" in analyzer.viewsets
        querysets = analyzer.viewsets["BookViewSet"]
        
        method_qs = next((qs for qs in querysets if qs.method_name == 'get_queryset'), None)
        assert method_qs is not None
        assert method_qs.has_prefetch_related
        assert len(method_qs.prefetch_objects) >= 2
        
        # Check Prefetch objects
        review_prefetch = next((p for p in method_qs.prefetch_objects if p.get('lookup') == 'reviews'), None)
        tags_prefetch = next((p for p in method_qs.prefetch_objects if p.get('lookup') == 'tags'), None)
        
        assert review_prefetch is not None
        assert tags_prefetch is not None
        assert tags_prefetch.get('to_attr') == 'cached_tags'
    
    def test_import_handling(self):
        """Test import statement handling"""
        code = """
from rest_framework import serializers as drf_serializers
from django.db import models
import django.db.models.fields

class TestSerializer(drf_serializers.ModelSerializer):
    test_field = drf_serializers.CharField()
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        # Check that imports are tracked
        assert 'drf_serializers' in analyzer.imports
        assert 'models' in analyzer.imports
        
        # Check that aliased imports work
        assert "TestSerializer" in analyzer.serializers
    
    def test_complex_field_paths(self):
        """Test complex field path detection with source parameter"""
        code = """
from rest_framework import serializers

class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')
    publisher_country = serializers.CharField(source='publisher.address.country')
    category_name = serializers.CharField(source='category.name')
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'publisher_country', 'category_name']
"""
        
        analyzer = DjangoASTAnalyzer()
        analyzer.analyze_file("test.py", code)
        
        fields = analyzer.serializers["BookSerializer"]
        
        author_name = next((f for f in fields if f.name == 'author_name'), None)
        publisher_country = next((f for f in fields if f.name == 'publisher_country'), None)
        
        assert author_name is not None
        assert author_name.source == 'author.name'
        assert author_name.field_path == 'author.name'
        
        assert publisher_country is not None
        assert publisher_country.source == 'publisher.address.country'
        assert publisher_country.field_path == 'publisher.address.country'