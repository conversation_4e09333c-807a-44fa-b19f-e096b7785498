"""
Tests for output formatters
"""

import json
import pytest

from django_n1_guard.formatters import (
    <PERSON><PERSON><PERSON><PERSON>er, J<PERSON><PERSON>ormatter, GitHubF<PERSON>atter, 
    SARIFFormatter, CompactFormatter
)
from django_n1_guard.models import N1Issue, AnalysisResult


@pytest.fixture
def sample_issues():
    """Create sample N+1 issues for testing"""
    return [
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet", 
            field_name="author_name",
            field_path="author.name",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10,
            severity="info",
            confidence=0.9,
            suggestion=".select_related('author')"
        ),
        N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="reviews", 
            field_path="reviews",
            issue_type="prefetch_related",
            file_path="/app/serializers.py",
            line_number=15,
            severity="warning", 
            confidence=0.8,
            suggestion=".prefetch_related('reviews')"
        ),
        N1Issue(
            serializer_name="AuthorSerializer",
            view_name="AuthorViewSet",
            field_name="book_count",
            field_path="books",
            issue_type="prefetch_related", 
            file_path="/app/views.py",
            line_number=25,
            severity="error",
            confidence=0.7,
            suggestion=".prefetch_related('books')"
        )
    ]


@pytest.fixture 
def sample_result(sample_issues):
    """Create sample analysis result"""
    return AnalysisResult(
        issues=sample_issues,
        files_analyzed=2,
        total_lines=150,
        analysis_time=0.5
    )


class TestTextFormatter:
    """Test the text formatter"""
    
    def test_empty_result(self):
        """Test formatting empty result"""
        formatter = TextFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        assert output == ""
    
    def test_format_with_issues(self, sample_result):
        """Test formatting result with issues"""
        formatter = TextFormatter()
        output = formatter.format(sample_result)
        
        assert "🚨 N+1 Query Issues Detected:" in output
        assert "BookSerializer" in output
        assert "author_name" in output
        assert "select_related" in output
        assert ".select_related('author')" in output
        assert "📊 Summary:" in output
        assert "Total issues: 3" in output
    
    def test_grouping_by_file(self, sample_result):
        """Test that issues are grouped by file"""
        formatter = TextFormatter()
        output = formatter.format(sample_result)
        
        assert "📁 /app/serializers.py" in output
        assert "📁 /app/views.py" in output
    
    def test_severity_icons(self, sample_result):
        """Test severity icon display"""
        formatter = TextFormatter()
        output = formatter.format(sample_result)
        
        # Should contain different severity icons
        assert "ℹ️" in output or "❌" in output or "⚠️" in output


class TestJSONFormatter:
    """Test the JSON formatter"""
    
    def test_empty_result(self):
        """Test JSON formatting of empty result"""
        formatter = JSONFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        data = json.loads(output)
        
        assert data['summary']['total_issues'] == 0
        assert data['issues'] == []
    
    def test_format_with_issues(self, sample_result):
        """Test JSON formatting with issues"""
        formatter = JSONFormatter()
        output = formatter.format(sample_result)
        data = json.loads(output)
        
        assert data['summary']['total_issues'] == 3
        assert data['summary']['files_analyzed'] == 2
        assert len(data['issues']) == 3
        
        # Check first issue structure
        issue = data['issues'][0]
        assert 'id' in issue
        assert 'file' in issue
        assert 'line' in issue
        assert 'serializer' in issue
        assert 'view' in issue
        assert 'field' in issue
        assert 'type' in issue
        assert 'severity' in issue
        assert 'confidence' in issue
        assert 'suggestion' in issue
    
    def test_statistics_section(self, sample_result):
        """Test statistics section in JSON output"""
        formatter = JSONFormatter()
        output = formatter.format(sample_result)
        data = json.loads(output)
        
        stats = data['statistics']
        assert 'by_severity' in stats
        assert 'by_type' in stats
        assert 'by_file' in stats
        
        # Check severity counts
        assert stats['by_severity']['info'] == 1
        assert stats['by_severity']['warning'] == 1 
        assert stats['by_severity']['error'] == 1
        
        # Check type counts
        assert stats['by_type']['select_related'] == 1
        assert stats['by_type']['prefetch_related'] == 2


class TestGitHubFormatter:
    """Test the GitHub Actions formatter"""
    
    def test_empty_result(self):
        """Test GitHub formatting of empty result"""
        formatter = GitHubFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        assert output == ""
    
    def test_format_with_issues(self, sample_result):
        """Test GitHub formatting with issues"""
        formatter = GitHubFormatter()
        output = formatter.format(result)
        
        lines = output.strip().split('\n')
        assert len(lines) == 3
        
        # Check annotation format
        for line in lines:
            assert line.startswith('::')
            assert 'file=' in line
            assert 'line=' in line
            assert '::' in line
    
    def test_severity_mapping(self, sample_result):
        """Test severity to GitHub level mapping"""
        formatter = GitHubFormatter()
        output = formatter.format(sample_result)
        
        # Error severity should map to 'error'
        assert '::error ' in output
        # Warning and info should map to 'warning'
        assert '::warning ' in output


class TestSARIFFormatter:
    """Test the SARIF formatter"""
    
    def test_empty_result(self):
        """Test SARIF formatting of empty result"""
        formatter = SARIFFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        data = json.loads(output)
        
        assert data['$schema']
        assert data['version'] == '2.1.0'
        assert len(data['runs']) == 1
        assert data['runs'][0]['results'] == []
    
    def test_format_with_issues(self, sample_result):
        """Test SARIF formatting with issues"""
        formatter = SARIFFormatter()
        output = formatter.format(sample_result)
        data = json.loads(output)
        
        run = data['runs'][0]
        assert len(run['results']) == 3
        assert len(run['tool']['driver']['rules']) == 2
        
        # Check first result
        result = run['results'][0]
        assert 'ruleId' in result
        assert 'level' in result
        assert 'message' in result
        assert 'locations' in result
        assert 'properties' in result
    
    def test_rule_definitions(self):
        """Test SARIF rule definitions"""
        formatter = SARIFFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        data = json.loads(output)
        
        rules = data['runs'][0]['tool']['driver']['rules']
        assert len(rules) == 2
        
        rule_ids = [rule['id'] for rule in rules]
        assert 'django-n1-select-related' in rule_ids
        assert 'django-n1-prefetch-related' in rule_ids


class TestCompactFormatter:
    """Test the compact formatter"""
    
    def test_empty_result(self):
        """Test compact formatting of empty result"""
        formatter = CompactFormatter()
        result = AnalysisResult()
        
        output = formatter.format(result)
        assert output == "No issues found"
    
    def test_format_with_issues(self, sample_result):
        """Test compact formatting with issues"""
        formatter = CompactFormatter()
        output = formatter.format(sample_result)
        
        lines = output.strip().split('\n')
        assert len(lines) == 3
        
        # Check compact format: file:line:severity:message
        for line in lines:
            parts = line.split(':')
            assert len(parts) >= 4
            assert parts[0].startswith('/')  # file path
            assert parts[1].isdigit()       # line number
            assert parts[2] in ['info', 'warning', 'error']  # severity


class TestFormatterEdgeCases:
    """Test edge cases for formatters"""
    
    def test_unicode_handling(self):
        """Test handling of unicode characters"""
        issue = N1Issue(
            serializer_name="BookSerializer",
            view_name="BookViewSet",
            field_name="제목",  # Korean characters
            field_path="제목",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10,
            severity="info"
        )
        
        result = AnalysisResult(issues=[issue])
        
        # Test all formatters handle unicode
        formatters = [TextFormatter(), JSONFormatter(), GitHubFormatter(), SARIFFormatter()]
        
        for formatter in formatters:
            output = formatter.format(result)
            assert output  # Should not crash
            assert "제목" in output
    
    def test_special_characters_in_paths(self):
        """Test handling of special characters in file paths"""
        issue = N1Issue(
            serializer_name="BookSerializer", 
            view_name="BookViewSet",
            field_name="author",
            field_path="author",
            issue_type="select_related",
            file_path="/app/my project/serializers.py",  # Space in path
            line_number=10,
            severity="info"
        )
        
        result = AnalysisResult(issues=[issue])
        
        formatter = GitHubFormatter()
        output = formatter.format(result)
        
        # Should handle spaces in file paths
        assert "file=/app/my project/serializers.py" in output
    
    def test_very_long_suggestions(self):
        """Test handling of very long suggestions"""
        issue = N1Issue(
            serializer_name="VeryLongSerializerNameThatExceedsNormalLength",
            view_name="VeryLongViewSetNameThatExceedsNormalLength", 
            field_name="very_long_field_name_that_exceeds_normal_length",
            field_path="very.long.field.path.that.exceeds.normal.length",
            issue_type="select_related",
            file_path="/app/serializers.py",
            line_number=10,
            severity="info",
            suggestion=".select_related('very_long_field_name_that_exceeds_normal_length_and_continues_even_longer')"
        )
        
        result = AnalysisResult(issues=[issue])
        
        # All formatters should handle long content gracefully
        formatters = [TextFormatter(), JSONFormatter(), CompactFormatter()]
        
        for formatter in formatters:
            output = formatter.format(result)
            assert output  # Should not crash
            assert len(output) > 0