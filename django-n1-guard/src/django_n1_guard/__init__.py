"""
Django N+1 Guard - Static analyzer to detect N+1 queries in Django/DRF projects
"""

__version__ = "0.1.0"
__author__ = "Django N+1 Guard Contributors"
__email__ = "<EMAIL>"

from .detector import N1Detector
from .models import (
    N1Issue,
    AnalysisResult,
    SerializerField,
    QuerySetUsage,
    SerializerUsage,
    ModelField,
)
from .analyzer import DjangoASTAnalyzer
from .formatters import (
    TextFormatter,
    JSONFormatter,
    GitHubFormatter,
    SARIFFormatter,
    CompactFormatter,
)

__all__ = [
    "N1Detector",
    "N1Issue",
    "AnalysisResult",
    "SerializerField",
    "QuerySetUsage", 
    "SerializerUsage",
    "ModelField",
    "DjangoASTAnalyzer",
    "TextFormatter",
    "J<PERSON>NFormatter",
    "GitHubFormatter", 
    "SARIFFormatter",
    "CompactFormatter",
]
