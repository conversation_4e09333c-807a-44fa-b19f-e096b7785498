"""
Core N+1 detection engine
"""

import ast
import os
import time
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple

from .analyzer import DjangoASTAnalyzer
from .models import N1Issue, AnalysisResult, SerializerField, QuerySetUsage


class N1Detector:
    """Main N+1 query detection engine"""
    
    def __init__(self, exclude_patterns: Optional[List[str]] = None):
        """
        Initialize the detector
        
        Args:
            exclude_patterns: Patterns to exclude from analysis
        """
        self.exclude_patterns = exclude_patterns or [
            'migrations', '__pycache__', '.git', 'venv', 'env', 
            'node_modules', '.pytest_cache', '.tox', 'build', 'dist'
        ]
        self.analyzer = DjangoASTAnalyzer()
        self.issues: Set[N1Issue] = set()
        
    def analyze_file(self, file_path: Path) -> List[N1Issue]:
        """
        Analyze a single Python file for N+1 issues
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of detected issues
        """
        if not file_path.exists() or not file_path.suffix == '.py':
            return []
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Reset analyzer state for new file
            self.analyzer = DjangoASTAnalyzer()
            self.analyzer.analyze_file(str(file_path), content)
            
            # Detect N+1 issues in this file
            file_issues = self._detect_n1_issues_in_file(str(file_path))
            self.issues.update(file_issues)
            
            return list(file_issues)
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return []
    
    def analyze_directory(self, directory: Path) -> AnalysisResult:
        """
        Analyze all Python files in a directory recursively
        
        Args:
            directory: Directory to analyze
            
        Returns:
            Complete analysis result
        """
        start_time = time.time()
        files_analyzed = 0
        total_lines = 0
        
        # Reset state
        self.issues.clear()
        all_serializers = {}
        all_viewsets = {}
        all_models = {}
        
        for py_file in directory.rglob('*.py'):
            # Skip excluded patterns
            if any(pattern in str(py_file) for pattern in self.exclude_patterns):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    total_lines += len(content.splitlines())
                    
                # Analyze this file
                analyzer = DjangoASTAnalyzer()
                analyzer.analyze_file(str(py_file), content)
                
                # Merge results
                all_serializers.update(analyzer.serializers)
                all_viewsets.update(analyzer.viewsets)
                all_models.update(analyzer.models)
                
                # Detect issues
                file_issues = self._detect_n1_issues_with_analyzer(analyzer, str(py_file))
                self.issues.update(file_issues)
                
                files_analyzed += 1
                
            except Exception as e:
                print(f"Error analyzing {py_file}: {e}")
                continue
        
        # Cross-file analysis
        cross_file_issues = self._detect_cross_file_issues(
            all_serializers, all_viewsets, all_models
        )
        self.issues.update(cross_file_issues)
        
        analysis_time = time.time() - start_time
        
        return AnalysisResult(
            issues=list(self.issues),
            serializers=all_serializers,
            viewsets=all_viewsets,
            models=all_models,
            files_analyzed=files_analyzed,
            total_lines=total_lines,
            analysis_time=analysis_time
        )
    
    def _detect_n1_issues_in_file(self, file_path: str) -> Set[N1Issue]:
        """Detect N+1 issues within a single file"""
        return self._detect_n1_issues_with_analyzer(self.analyzer, file_path)
    
    def _detect_n1_issues_with_analyzer(
        self, analyzer: DjangoASTAnalyzer, file_path: str
    ) -> Set[N1Issue]:
        """Detect N+1 issues using the given analyzer"""
        issues = set()
        
        # Find ViewSet-Serializer relationships
        view_serializer_map = self._build_view_serializer_map(analyzer)
        
        for view_name, serializer_names in view_serializer_map.items():
            # Get QuerySet usage for this view
            view_querysets = analyzer.viewsets.get(view_name, [])
            
            for serializer_name in serializer_names:
                if serializer_name not in analyzer.serializers:
                    continue
                    
                serializer_fields = analyzer.serializers[serializer_name]
                
                # Check each relation field against QuerySet optimizations
                for field in serializer_fields:
                    if not field.is_relation:
                        continue
                    
                    # Check if any QuerySet usage covers this field
                    is_optimized = any(
                        qs.has_optimization_for(field) for qs in view_querysets
                    )
                    
                    if not is_optimized:
                        issue_type = (
                            'select_related' if field.requires_select_related
                            else 'prefetch_related'
                        )
                        
                        issue = N1Issue(
                            serializer_name=serializer_name,
                            view_name=view_name,
                            field_name=field.name,
                            field_path=field.field_path,
                            issue_type=issue_type,
                            file_path=file_path,
                            line_number=field.line_number,
                            severity=self._calculate_severity(field),
                            confidence=self._calculate_confidence(field, view_querysets)
                        )
                        issue.suggestion = issue.format_suggestion()
                        issues.add(issue)
        
        return issues
    
    def _detect_cross_file_issues(
        self, 
        all_serializers: Dict[str, List[SerializerField]],
        all_viewsets: Dict[str, List[QuerySetUsage]],
        all_models: Dict[str, List]
    ) -> Set[N1Issue]:
        """Detect N+1 issues across multiple files"""
        issues = set()
        
        # More sophisticated cross-file analysis
        for view_name, querysets in all_viewsets.items():
            # Try to find related serializers by naming convention
            potential_serializers = self._find_related_serializers(
                view_name, all_serializers
            )
            
            for serializer_name in potential_serializers:
                if serializer_name not in all_serializers:
                    continue
                    
                fields = all_serializers[serializer_name]
                
                for field in fields:
                    if not field.is_relation:
                        continue
                    
                    # Check optimization across all querysets for this view
                    is_optimized = any(
                        qs.has_optimization_for(field) for qs in querysets
                    )
                    
                    if not is_optimized:
                        issue_type = (
                            'select_related' if field.requires_select_related
                            else 'prefetch_related'
                        )
                        
                        # Find the first QuerySet usage for line number
                        line_number = querysets[0].line_number if querysets else 0
                        file_path = querysets[0].file_path if querysets else ""
                        
                        issue = N1Issue(
                            serializer_name=serializer_name,
                            view_name=view_name,
                            field_name=field.name,
                            field_path=field.field_path,
                            issue_type=issue_type,
                            file_path=file_path,
                            line_number=line_number,
                            severity=self._calculate_severity(field),
                            confidence=self._calculate_confidence(field, querysets)
                        )
                        issue.suggestion = issue.format_suggestion()
                        issues.add(issue)
        
        return issues
    
    def _build_view_serializer_map(
        self, analyzer: DjangoASTAnalyzer
    ) -> Dict[str, List[str]]:
        """Build mapping between ViewSets and their Serializers"""
        view_serializer_map = {}
        
        # Direct mappings from serializer_usage
        for usage in analyzer.serializer_usage:
            if usage.view_name not in view_serializer_map:
                view_serializer_map[usage.view_name] = []
            if usage.serializer_name not in view_serializer_map[usage.view_name]:
                view_serializer_map[usage.view_name].append(usage.serializer_name)
        
        # Heuristic mappings based on naming for all viewsets
        for view_name in analyzer.viewsets.keys():
            if view_name not in view_serializer_map:
                view_serializer_map[view_name] = []
            
            # Always try to find related serializers
            related_serializers = self._find_related_serializers(
                view_name, analyzer.serializers
            )
            for serializer_name in related_serializers:
                if serializer_name not in view_serializer_map[view_name]:
                    view_serializer_map[view_name].append(serializer_name)
        
        return view_serializer_map
    
    def _find_related_serializers(
        self, view_name: str, serializers: Dict[str, List[SerializerField]]
    ) -> List[str]:
        """Find serializers related to a view by naming convention"""
        base_name = (
            view_name.replace('ViewSet', '')
            .replace('APIView', '')
            .replace('View', '')
        )
        
        related = []
        for serializer_name in serializers.keys():
            # Direct name matching
            if base_name in serializer_name:
                related.append(serializer_name)
            # Reverse matching
            elif any(part in view_name for part in serializer_name.split('Serializer')[0].split()):
                related.append(serializer_name)
        
        return related
    
    def _calculate_severity(self, field: SerializerField) -> str:
        """Calculate issue severity based on field characteristics"""
        if field.is_many and field.field_type == 'SerializerMethodField':
            return 'error'  # Method fields with many=True are likely expensive
        elif field.is_many:
            return 'warning'  # Many-to-many relationships
        else:
            return 'info'  # Simple foreign keys
    
    def _calculate_confidence(
        self, field: SerializerField, querysets: List[QuerySetUsage]
    ) -> float:
        """Calculate confidence score for the issue"""
        confidence = 1.0
        
        # Lower confidence for SerializerMethodField
        if field.field_type == 'SerializerMethodField':
            confidence *= 0.8
        
        # Lower confidence if no querysets found
        if not querysets:
            confidence *= 0.6
        
        # Lower confidence for complex field paths
        if field.source and '__' in field.source:
            confidence *= 0.9
        
        return confidence
    
    def get_statistics(self) -> Dict[str, int]:
        """Get analysis statistics"""
        if not self.issues:
            return {}
        
        stats = {
            'total_issues': len(self.issues),
            'select_related_issues': len([i for i in self.issues if i.issue_type == 'select_related']),
            'prefetch_related_issues': len([i for i in self.issues if i.issue_type == 'prefetch_related']),
            'high_confidence_issues': len([i for i in self.issues if i.confidence >= 0.8]),
            'error_severity': len([i for i in self.issues if i.severity == 'error']),
            'warning_severity': len([i for i in self.issues if i.severity == 'warning']),
            'info_severity': len([i for i in self.issues if i.severity == 'info']),
        }
        
        return stats