"""
Command Line Interface for django-n1-guard
"""

import json
import sys
import time
from pathlib import Path
from typing import Optional

import click
from colorama import Fore, Style, init

from .detector import N1Detector
from .formatters import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, SARIFFormatter
from .models import AnalysisResult

# Initialize colorama for cross-platform colored output
init(autoreset=True)


@click.command()
@click.argument('path', type=click.Path(exists=True, path_type=Path))
@click.option(
    '--format', 'output_format',
    type=click.Choice(['text', 'json', 'github', 'sarif'], case_sensitive=False),
    default='text',
    help='Output format (default: text)'
)
@click.option(
    '--exclude',
    multiple=True,
    help='Patterns to exclude from analysis (can be used multiple times)'
)
@click.option(
    '--fail-on-issues',
    is_flag=True,
    help='Exit with non-zero code if issues found (for CI/CD)'
)
@click.option(
    '--min-confidence',
    type=click.FloatRange(0.0, 1.0),
    default=0.5,
    help='Minimum confidence threshold for reporting issues (0.0-1.0)'
)
@click.option(
    '--severity',
    type=click.Choice(['info', 'warning', 'error'], case_sensitive=False),
    multiple=True,
    help='Filter by severity levels'
)
@click.option(
    '--config',
    type=click.Path(exists=True, path_type=Path),
    help='Path to configuration file'
)
@click.option(
    '--quiet', '-q',
    is_flag=True,
    help='Suppress non-essential output'
)
@click.option(
    '--verbose', '-v',
    is_flag=True,
    help='Enable verbose output'
)
@click.option(
    '--stats',
    is_flag=True,
    help='Show analysis statistics'
)
@click.version_option(version='0.1.0', prog_name='django-n1-guard')
def main(
    path: Path,
    output_format: str,
    exclude: tuple,
    fail_on_issues: bool,
    min_confidence: float,
    severity: tuple,
    config: Optional[Path],
    quiet: bool,
    verbose: bool,
    stats: bool
):
    """
    Detect N+1 query issues in Django/DRF projects.
    
    PATH can be a file or directory to analyze.
    """
    if not quiet:
        click.echo(f"{Fore.CYAN}🔍 Django N+1 Guard{Style.RESET_ALL}")
        click.echo(f"Analyzing: {path}")
    
    # Load configuration
    config_data = {}
    if config:
        config_data = _load_config(config)
    
    # Merge CLI options with config
    exclude_patterns = list(exclude) + config_data.get('exclude', [])
    
    # Initialize detector
    detector = N1Detector(exclude_patterns=exclude_patterns or None)
    
    try:
        # Perform analysis
        if path.is_file():
            if not quiet:
                click.echo("Analyzing single file...")
            issues = detector.analyze_file(path)
            result = AnalysisResult(
                issues=issues,
                files_analyzed=1,
                total_lines=_count_lines(path)
            )
        else:
            if not quiet:
                click.echo("Analyzing directory...")
            result = detector.analyze_directory(path)
        
        # Filter results
        result = _filter_results(result, min_confidence, severity)
        
        # Show statistics if requested
        if stats and not quiet:
            _show_statistics(result, detector)
        
        # Generate and output report
        formatter = _get_formatter(output_format)
        report = formatter.format(result)
        
        if report:
            click.echo(report)
        elif not quiet:
            click.echo(f"{Fore.GREEN}✅ No N+1 query issues detected!{Style.RESET_ALL}")
        
        # Exit with appropriate code
        if fail_on_issues and result.total_issues > 0:
            sys.exit(1)
        
    except KeyboardInterrupt:
        click.echo(f"\n{Fore.YELLOW}⚠️  Analysis interrupted by user{Style.RESET_ALL}")
        sys.exit(130)
    except Exception as e:
        click.echo(f"{Fore.RED}❌ Error during analysis: {e}{Style.RESET_ALL}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def _load_config(config_path: Path) -> dict:
    """Load configuration from file"""
    try:
        import yaml
        
        if config_path.suffix in ['.yml', '.yaml']:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        elif config_path.suffix == '.json':
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            click.echo(f"{Fore.YELLOW}⚠️  Unsupported config format: {config_path.suffix}{Style.RESET_ALL}")
            return {}
    except Exception as e:
        click.echo(f"{Fore.RED}❌ Error loading config: {e}{Style.RESET_ALL}", err=True)
        return {}


def _count_lines(file_path: Path) -> int:
    """Count lines in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except Exception:
        return 0


def _filter_results(
    result: AnalysisResult, 
    min_confidence: float, 
    severity_filter: tuple
) -> AnalysisResult:
    """Filter analysis results based on criteria"""
    if not result.issues:
        return result
    
    filtered_issues = []
    
    for issue in result.issues:
        # Filter by confidence
        if issue.confidence < min_confidence:
            continue
        
        # Filter by severity
        if severity_filter and issue.severity not in severity_filter:
            continue
        
        filtered_issues.append(issue)
    
    # Create new result with filtered issues
    return AnalysisResult(
        issues=filtered_issues,
        serializers=result.serializers,
        viewsets=result.viewsets,
        models=result.models,
        files_analyzed=result.files_analyzed,
        total_lines=result.total_lines,
        analysis_time=result.analysis_time
    )


def _show_statistics(result: AnalysisResult, detector: N1Detector):
    """Show analysis statistics"""
    stats = detector.get_statistics()
    
    click.echo(f"\n{Fore.CYAN}📊 Analysis Statistics:{Style.RESET_ALL}")
    click.echo(f"  Files analyzed: {result.files_analyzed}")
    click.echo(f"  Total lines: {result.total_lines:,}")
    click.echo(f"  Analysis time: {result.analysis_time:.2f}s")
    click.echo(f"  Serializers found: {len(result.serializers)}")
    click.echo(f"  ViewSets found: {len(result.viewsets)}")
    click.echo(f"  Models found: {len(result.models)}")
    
    if stats:
        click.echo(f"\n{Fore.YELLOW}🚨 Issues Breakdown:{Style.RESET_ALL}")
        click.echo(f"  Total issues: {stats.get('total_issues', 0)}")
        click.echo(f"  Select-related issues: {stats.get('select_related_issues', 0)}")
        click.echo(f"  Prefetch-related issues: {stats.get('prefetch_related_issues', 0)}")
        click.echo(f"  High confidence: {stats.get('high_confidence_issues', 0)}")
        click.echo(f"  Error severity: {stats.get('error_severity', 0)}")
        click.echo(f"  Warning severity: {stats.get('warning_severity', 0)}")
        click.echo(f"  Info severity: {stats.get('info_severity', 0)}")


def _get_formatter(format_name: str):
    """Get the appropriate formatter"""
    formatters = {
        'text': TextFormatter(),
        'json': JSONFormatter(),
        'github': GitHubFormatter(),
        'sarif': SARIFFormatter()
    }
    return formatters.get(format_name, TextFormatter())


# Additional CLI commands for specific workflows

@click.group()
def cli():
    """Django N+1 Guard - Detect N+1 query issues in Django/DRF projects"""
    pass


@cli.command()
@click.argument('path', type=click.Path(exists=True, path_type=Path))
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output file path')
def generate_config(path: Path, output: Optional[Path]):
    """Generate a configuration file based on project analysis"""
    config = {
        'exclude': [
            'migrations',
            '__pycache__',
            '.git',
            'venv',
            'env',
            'node_modules',
            '.pytest_cache',
            '.tox',
            'build',
            'dist'
        ],
        'min_confidence': 0.7,
        'severity': ['warning', 'error'],
        'rules': {
            'require_select_related': True,
            'require_prefetch_related': True,
            'check_serializer_method_fields': True
        }
    }
    
    output_path = output or path / 'n1guard.yml'
    
    try:
        import yaml
        with open(output_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        click.echo(f"✅ Configuration saved to: {output_path}")
    except ImportError:
        # Fallback to JSON if PyYAML not available
        output_path = output_path.with_suffix('.json')
        with open(output_path, 'w') as f:
            json.dump(config, f, indent=2)
        click.echo(f"✅ Configuration saved to: {output_path}")


@cli.command()
def version():
    """Show version information"""
    click.echo("django-n1-guard 0.1.0")
    click.echo("A static analyzer to detect N+1 queries in Django/DRF projects")


# Make main command available directly
cli.add_command(main, name='analyze')

if __name__ == '__main__':
    main()