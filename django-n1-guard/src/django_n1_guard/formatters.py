"""
Output formatters for different report types
"""

import json
from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Dict, List

from colorama import Fore, Style

from .models import AnalysisResult, N1Issue


class BaseFormatter(ABC):
    """Base class for all formatters"""
    
    @abstractmethod
    def format(self, result: AnalysisResult) -> str:
        """Format the analysis result"""
        pass


class TextFormatter(BaseFormatter):
    """Human-readable text formatter with colors"""
    
    def format(self, result: AnalysisResult) -> str:
        if not result.issues:
            return ""
        
        lines = []
        lines.append(f"{Fore.RED}🚨 N+1 Query Issues Detected:{Style.RESET_ALL}\n")
        
        # Group issues by file
        issues_by_file = self._group_by_file(result.issues)
        
        for file_path, file_issues in issues_by_file.items():
            lines.append(f"\n{Fore.CYAN}📁 {file_path}{Style.RESET_ALL}")
            
            # Sort issues by line number
            sorted_issues = sorted(file_issues, key=lambda x: x.line_number)
            
            for issue in sorted_issues:
                severity_icon = self._get_severity_icon(issue.severity)
                confidence_str = f"({issue.confidence:.1%})" if issue.confidence < 1.0 else ""
                
                lines.append(f"  {severity_icon} Line {issue.line_number}: {issue.view_name}")
                lines.append(f"     Serializer: {Fore.YELLOW}{issue.serializer_name}{Style.RESET_ALL}")
                lines.append(f"     Field: {Fore.MAGENTA}{issue.field_name}{Style.RESET_ALL} {confidence_str}")
                lines.append(f"     Missing: {Fore.RED}{issue.issue_type}{Style.RESET_ALL}")
                
                if issue.suggestion:
                    lines.append(f"     Suggestion: {Fore.GREEN}{issue.suggestion}{Style.RESET_ALL}")
                
                lines.append("")
        
        # Summary
        lines.append(self._format_summary(result))
        
        return "\n".join(lines)
    
    def _group_by_file(self, issues: List[N1Issue]) -> Dict[str, List[N1Issue]]:
        """Group issues by file path"""
        grouped = defaultdict(list)
        for issue in issues:
            grouped[issue.file_path].append(issue)
        return dict(grouped)
    
    def _get_severity_icon(self, severity: str) -> str:
        """Get icon for severity level"""
        icons = {
            'error': f"{Fore.RED}❌{Style.RESET_ALL}",
            'warning': f"{Fore.YELLOW}⚠️{Style.RESET_ALL}",
            'info': f"{Fore.BLUE}ℹ️{Style.RESET_ALL}"
        }
        return icons.get(severity, "•")
    
    def _format_summary(self, result: AnalysisResult) -> str:
        """Format summary section"""
        lines = []
        lines.append(f"{Fore.CYAN}📊 Summary:{Style.RESET_ALL}")
        lines.append(f"  Total issues: {Fore.RED}{len(result.issues)}{Style.RESET_ALL}")
        
        # Group by severity
        by_severity = result.issues_by_severity
        if by_severity['error']:
            lines.append(f"  Errors: {Fore.RED}{len(by_severity['error'])}{Style.RESET_ALL}")
        if by_severity['warning']:
            lines.append(f"  Warnings: {Fore.YELLOW}{len(by_severity['warning'])}{Style.RESET_ALL}")
        if by_severity['info']:
            lines.append(f"  Info: {Fore.BLUE}{len(by_severity['info'])}{Style.RESET_ALL}")
        
        # Group by type
        select_related = [i for i in result.issues if i.issue_type == 'select_related']
        prefetch_related = [i for i in result.issues if i.issue_type == 'prefetch_related']
        
        if select_related:
            lines.append(f"  Select-related issues: {len(select_related)}")
        if prefetch_related:
            lines.append(f"  Prefetch-related issues: {len(prefetch_related)}")
        
        return "\n".join(lines)


class JSONFormatter(BaseFormatter):
    """JSON formatter for machine-readable output"""
    
    def format(self, result: AnalysisResult) -> str:
        data = {
            'summary': {
                'total_issues': result.total_issues,
                'files_analyzed': result.files_analyzed,
                'total_lines': result.total_lines,
                'analysis_time': result.analysis_time
            },
            'issues': [self._format_issue(issue) for issue in result.issues],
            'statistics': {
                'by_severity': {
                    severity: len(issues) 
                    for severity, issues in result.issues_by_severity.items()
                },
                'by_type': {
                    'select_related': len([i for i in result.issues if i.issue_type == 'select_related']),
                    'prefetch_related': len([i for i in result.issues if i.issue_type == 'prefetch_related'])
                },
                'by_file': {
                    file_path: len(issues)
                    for file_path, issues in result.issues_by_file.items()
                }
            }
        }
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _format_issue(self, issue: N1Issue) -> dict:
        """Format a single issue for JSON output"""
        return {
            'id': issue.unique_id,
            'file': issue.file_path,
            'line': issue.line_number,
            'serializer': issue.serializer_name,
            'view': issue.view_name,
            'field': issue.field_name,
            'field_path': issue.field_path,
            'type': issue.issue_type,
            'severity': issue.severity,
            'confidence': issue.confidence,
            'suggestion': issue.suggestion,
            'message': f"N+1 Query: {issue.serializer_name}.{issue.field_name} missing {issue.issue_type}"
        }


class GitHubFormatter(BaseFormatter):
    """GitHub Actions workflow commands formatter"""
    
    def format(self, result: AnalysisResult) -> str:
        if not result.issues:
            return ""
        
        lines = []
        
        for issue in result.issues:
            level = 'error' if issue.severity == 'error' else 'warning'
            message = f"N+1 Query: {issue.serializer_name}.{issue.field_name} missing {issue.issue_type}"
            
            if issue.suggestion:
                message += f" - Suggestion: {issue.suggestion}"
            
            # GitHub Actions annotation format
            annotation = f"::{level} file={issue.file_path},line={issue.line_number}::{message}"
            lines.append(annotation)
        
        return "\n".join(lines)


class SARIFFormatter(BaseFormatter):
    """SARIF (Static Analysis Results Interchange Format) formatter"""
    
    def format(self, result: AnalysisResult) -> str:
        sarif_data = {
            "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
            "version": "2.1.0",
            "runs": [
                {
                    "tool": {
                        "driver": {
                            "name": "django-n1-guard",
                            "version": "0.1.0",
                            "informationUri": "https://github.com/yourusername/django-n1-guard",
                            "rules": self._get_rules()
                        }
                    },
                    "results": [self._format_sarif_result(issue) for issue in result.issues]
                }
            ]
        }
        
        return json.dumps(sarif_data, indent=2)
    
    def _get_rules(self) -> List[dict]:
        """Get SARIF rule definitions"""
        return [
            {
                "id": "django-n1-select-related",
                "shortDescription": {
                    "text": "Missing select_related optimization"
                },
                "fullDescription": {
                    "text": "Django/DRF serializer field requires select_related() optimization to prevent N+1 queries"
                },
                "defaultConfiguration": {
                    "level": "warning"
                },
                "help": {
                    "text": "Add .select_related() to your QuerySet to optimize foreign key access",
                    "markdown": "Add `.select_related()` to your QuerySet to optimize foreign key access"
                }
            },
            {
                "id": "django-n1-prefetch-related",
                "shortDescription": {
                    "text": "Missing prefetch_related optimization"
                },
                "fullDescription": {
                    "text": "Django/DRF serializer field requires prefetch_related() optimization to prevent N+1 queries"
                },
                "defaultConfiguration": {
                    "level": "warning"
                },
                "help": {
                    "text": "Add .prefetch_related() to your QuerySet to optimize many-to-many or reverse foreign key access",
                    "markdown": "Add `.prefetch_related()` to your QuerySet to optimize many-to-many or reverse foreign key access"
                }
            }
        ]
    
    def _format_sarif_result(self, issue: N1Issue) -> dict:
        """Format a single issue for SARIF output"""
        rule_id = f"django-n1-{issue.issue_type.replace('_', '-')}"
        
        return {
            "ruleId": rule_id,
            "ruleIndex": 0 if issue.issue_type == 'select_related' else 1,
            "level": self._sarif_level(issue.severity),
            "message": {
                "text": f"Missing {issue.issue_type} for field '{issue.field_name}' in {issue.serializer_name}"
            },
            "locations": [
                {
                    "physicalLocation": {
                        "artifactLocation": {
                            "uri": issue.file_path
                        },
                        "region": {
                            "startLine": issue.line_number,
                            "startColumn": 1
                        }
                    }
                }
            ],
            "properties": {
                "confidence": issue.confidence,
                "serializer": issue.serializer_name,
                "view": issue.view_name,
                "field": issue.field_name,
                "suggestion": issue.suggestion
            }
        }
    
    def _sarif_level(self, severity: str) -> str:
        """Convert severity to SARIF level"""
        mapping = {
            'info': 'note',
            'warning': 'warning',
            'error': 'error'
        }
        return mapping.get(severity, 'warning')


class CompactFormatter(BaseFormatter):
    """Compact formatter for CI/CD pipelines"""
    
    def format(self, result: AnalysisResult) -> str:
        if not result.issues:
            return "No issues found"
        
        lines = []
        
        for issue in result.issues:
            line = (
                f"{issue.file_path}:{issue.line_number}:"
                f"{issue.severity}:"
                f"{issue.serializer_name}.{issue.field_name} "
                f"missing {issue.issue_type}"
            )
            lines.append(line)
        
        return "\n".join(lines)