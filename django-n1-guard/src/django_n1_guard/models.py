"""
Data models for N+1 detection
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set


@dataclass
class SerializerField:
    """Represents a field in a Django REST Framework Serializer"""
    
    name: str
    field_type: str
    is_relation: bool
    is_many: bool = False
    source: Optional[str] = None
    queryset_model: Optional[str] = None
    line_number: int = 0
    depth: int = 0
    
    @property
    def requires_select_related(self) -> bool:
        """Check if field requires select_related optimization"""
        return self.is_relation and not self.is_many
    
    @property
    def requires_prefetch_related(self) -> bool:
        """Check if field requires prefetch_related optimization"""
        return self.is_relation and self.is_many
    
    @property
    def field_path(self) -> str:
        """Get the field path for optimization"""
        return self.source or self.name


@dataclass
class QuerySetUsage:
    """Represents QuerySet usage in a ViewSet/View"""
    
    model_name: str
    view_name: str
    method_name: str = ""
    has_select_related: bool = False
    has_prefetch_related: bool = False
    select_related_fields: Set[str] = field(default_factory=set)
    prefetch_related_fields: Set[str] = field(default_factory=set)
    prefetch_objects: List[Dict[str, Any]] = field(default_factory=list)
    annotations: Set[str] = field(default_factory=set)
    only_fields: Set[str] = field(default_factory=set)
    defer_fields: Set[str] = field(default_factory=set)
    line_number: int = 0
    file_path: str = ""
    
    def has_optimization_for(self, field: SerializerField) -> bool:
        """Check if QuerySet has optimization for a specific field"""
        field_path = field.field_path
        
        if field.requires_select_related:
            # For select_related, check if the base relation is covered
            # e.g., 'author.name' is covered by 'author'
            base_relation = field_path.split('.')[0] if '.' in field_path else field_path
            return (
                field_path in self.select_related_fields or
                base_relation in self.select_related_fields
            )
        elif field.requires_prefetch_related:
            # For prefetch_related, check exact match or in Prefetch objects
            base_relation = field_path.split('.')[0] if '.' in field_path else field_path
            return (
                field_path in self.prefetch_related_fields or
                base_relation in self.prefetch_related_fields or
                any(obj.get('lookup') == field_path for obj in self.prefetch_objects) or
                any(obj.get('lookup') == base_relation for obj in self.prefetch_objects)
            )
        return True


@dataclass
class SerializerUsage:
    """Represents Serializer usage in a ViewSet"""
    
    view_name: str
    serializer_name: str
    usage_type: str  # 'class_attr', 'get_serializer_class', 'action'
    action_name: Optional[str] = None
    line_number: int = 0


@dataclass
class ModelField:
    """Represents a field in a Django Model"""
    
    name: str
    field_type: str
    is_relation: bool
    related_model: Optional[str] = None
    related_name: Optional[str] = None
    is_many_to_many: bool = False
    is_foreign_key: bool = False
    is_one_to_one: bool = False
    line_number: int = 0


@dataclass
class N1Issue:
    """Represents a potential N+1 query issue"""
    
    serializer_name: str
    view_name: str
    field_name: str
    field_path: str
    issue_type: str  # 'select_related' or 'prefetch_related'
    file_path: str
    line_number: int
    severity: str = "warning"  # 'info', 'warning', 'error'
    suggestion: str = ""
    confidence: float = 1.0  # 0.0 to 1.0
    
    def __hash__(self):
        return hash((
            self.serializer_name,
            self.view_name,
            self.field_name,
            self.issue_type,
            self.file_path,
            self.line_number
        ))
    
    def __eq__(self, other):
        if not isinstance(other, N1Issue):
            return False
        return (
            self.serializer_name == other.serializer_name and
            self.view_name == other.view_name and
            self.field_name == other.field_name and
            self.issue_type == other.issue_type and
            self.file_path == other.file_path and
            self.line_number == other.line_number
        )
    
    @property
    def unique_id(self) -> str:
        """Generate unique identifier for the issue"""
        return f"{self.file_path}:{self.line_number}:{self.field_name}"
    
    def format_suggestion(self) -> str:
        """Format optimization suggestion"""
        if not self.suggestion:
            if self.issue_type == 'select_related':
                self.suggestion = f".select_related('{self.field_path}')"
            else:
                self.suggestion = f".prefetch_related('{self.field_path}')"
        return self.suggestion


@dataclass
class AnalysisResult:
    """Complete analysis result"""
    
    issues: List[N1Issue] = field(default_factory=list)
    serializers: Dict[str, List[SerializerField]] = field(default_factory=dict)
    viewsets: Dict[str, List[QuerySetUsage]] = field(default_factory=dict)
    models: Dict[str, List[ModelField]] = field(default_factory=dict)
    files_analyzed: int = 0
    total_lines: int = 0
    analysis_time: float = 0.0
    
    @property
    def total_issues(self) -> int:
        """Total number of issues found"""
        return len(self.issues)
    
    @property
    def issues_by_severity(self) -> Dict[str, List[N1Issue]]:
        """Group issues by severity"""
        result = {'info': [], 'warning': [], 'error': []}
        for issue in self.issues:
            result[issue.severity].append(issue)
        return result
    
    @property
    def issues_by_file(self) -> Dict[str, List[N1Issue]]:
        """Group issues by file path"""
        from collections import defaultdict
        result = defaultdict(list)
        for issue in self.issues:
            result[issue.file_path].append(issue)
        return dict(result)
