"""
AST-based analyzer for Django/DRF code
"""

import ast
from typing import Any, Dict, List, Optional, Set

from .models import <PERSON><PERSON>ield, QuerySetUsage, SerializerField, SerializerUsage


class DjangoASTAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing Django/DRF code patterns"""
    
    def __init__(self):
        self.serializers: Dict[str, List[SerializerField]] = {}
        self.models: Dict[str, List[ModelField]] = {}
        self.viewsets: Dict[str, List[QuerySetUsage]] = {}
        self.serializer_usage: List[SerializerUsage] = []
        
        # Analysis context
        self.current_class: Optional[str] = None
        self.current_method: Optional[str] = None
        self.current_file: str = ""
        self.imports: Dict[str, str] = {}
        self.in_viewset: bool = False
        self.in_serializer: bool = False
        self.in_model: bool = False
        self.current_decorators: List[str] = []
        
    def analyze_file(self, file_path: str, content: str) -> None:
        """Analyze a Python file"""
        self.current_file = file_path
        try:
            tree = ast.parse(content, filename=file_path)
            self.visit(tree)
        except SyntaxError as e:
            print(f"Syntax error in {file_path}: {e}")
            
    def visit_Import(self, node: ast.Import) -> None:
        """Process import statements"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports[name] = alias.name
        self.generic_visit(node)
        
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Process from...import statements"""
        module = node.module or ""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            full_name = f"{module}.{alias.name}"
            self.imports[name] = full_name
            
            # Track specific imports
            if alias.name == 'Prefetch':
                self.imports['Prefetch'] = 'django.db.models.Prefetch'
            elif 'serializers' in module:
                self.imports[name] = full_name
                
        self.generic_visit(node)
        
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Process class definitions"""
        old_class = self.current_class
        old_flags = (self.in_serializer, self.in_viewset, self.in_model)
        
        self.current_class = node.name
        self.current_decorators = [self._get_decorator_name(d) for d in node.decorator_list]
        
        # Determine class type and analyze
        if self._is_serializer(node):
            self.in_serializer = True
            self._analyze_serializer(node)
        elif self._is_model(node):
            self.in_model = True
            self._analyze_model(node)
        elif self._is_viewset(node):
            self.in_viewset = True
            self._analyze_viewset(node)
            
        self.generic_visit(node)
        
        # Restore context
        self.current_class = old_class
        self.in_serializer, self.in_viewset, self.in_model = old_flags
        self.current_decorators = []
        
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Process function/method definitions"""
        old_method = self.current_method
        self.current_method = node.name
        
        decorators = [self._get_decorator_name(d) for d in node.decorator_list]
        
        if self.in_viewset:
            if node.name == 'get_queryset':
                self._analyze_get_queryset(node)
            elif node.name == 'get_serializer_class':
                self._analyze_get_serializer_class(node)
            elif any('action' in d for d in decorators):
                self._analyze_action_method(node)
                
        elif self.in_serializer and node.name.startswith('get_'):
            self._analyze_serializer_method_field(node)
            
        self.generic_visit(node)
        self.current_method = old_method
        
    # Helper methods
    
    def _get_name(self, node: ast.AST) -> str:
        """Extract name from AST node"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            value = self._get_name(node.value)
            return f"{value}.{node.attr}" if value else node.attr
        elif isinstance(node, (ast.Constant, ast.Str)):
            return str(node.value if isinstance(node, ast.Constant) else node.s)
        return ""
        
    def _get_decorator_name(self, decorator: ast.AST) -> str:
        """Extract decorator name"""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Call):
            return self._get_name(decorator.func)
        elif isinstance(decorator, ast.Attribute):
            return decorator.attr
        return ""
        
    def _get_bool_value(self, node: ast.AST) -> bool:
        """Extract boolean value from AST node"""
        if isinstance(node, ast.Constant):
            return bool(node.value)
        elif hasattr(ast, 'NameConstant') and isinstance(node, ast.NameConstant):
            return bool(node.value)
        elif isinstance(node, ast.Name):
            return node.id == 'True'
        return False
        
    def _is_serializer(self, node: ast.ClassDef) -> bool:
        """Check if class is a DRF Serializer"""
        for base in node.bases:
            base_name = self._get_name(base)
            if 'Serializer' in base_name:
                return True
        return False
        
    def _is_model(self, node: ast.ClassDef) -> bool:
        """Check if class is a Django Model"""
        for base in node.bases:
            base_name = self._get_name(base)
            if base_name in ['Model', 'models.Model', 'AbstractModel']:
                return True
        return False
        
    def _is_viewset(self, node: ast.ClassDef) -> bool:
        """Check if class is a DRF ViewSet or APIView"""
        viewset_bases = [
            'ViewSet', 'ModelViewSet', 'ReadOnlyModelViewSet',
            'GenericViewSet', 'APIView', 'GenericAPIView',
            'ListAPIView', 'CreateAPIView', 'RetrieveAPIView',
            'UpdateAPIView', 'DestroyAPIView'
        ]
        
        for base in node.bases:
            base_name = self._get_name(base)
            if any(vb in base_name for vb in viewset_bases):
                return True
        return False
        
    # Serializer analysis
    
    def _analyze_serializer(self, node: ast.ClassDef) -> None:
        """Analyze Serializer class"""
        fields = []
        meta_info = {}
        
        for item in node.body:
            if isinstance(item, ast.ClassDef) and item.name == 'Meta':
                meta_info = self._analyze_meta_class(item)
            elif isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        field = self._analyze_serializer_field(
                            target.id, item.value, meta_info.get('depth', 0)
                        )
                        if field:
                            fields.append(field)
                            
        self.serializers[node.name] = fields
        
    def _analyze_meta_class(self, node: ast.ClassDef) -> Dict[str, Any]:
        """Analyze Meta class in Serializer"""
        meta_info = {}
        
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        if target.id == 'model':
                            meta_info['model'] = self._get_name(item.value)
                        elif target.id == 'fields':
                            if isinstance(item.value, ast.List):
                                meta_info['fields'] = [
                                    self._get_name(e) for e in item.value.elts
                                ]
                            elif isinstance(item.value, (ast.Constant, ast.Str)):
                                meta_info['fields'] = '__all__'
                        elif target.id == 'depth':
                            if isinstance(item.value, ast.Constant):
                                meta_info['depth'] = item.value.value
                                
        return meta_info
        
    def _analyze_serializer_field(
        self, field_name: str, value_node: ast.AST, depth: int = 0
    ) -> Optional[SerializerField]:
        """Analyze individual serializer field"""
        if not isinstance(value_node, ast.Call):
            return None
            
        func_name = self._get_name(value_node.func)
        
        # Check for relation fields
        relation_types = {
            'PrimaryKeyRelatedField', 'StringRelatedField',
            'SlugRelatedField', 'HyperlinkedRelatedField',
            'HyperlinkedIdentityField'
        }
        
        # Extract field parameters first
        is_many = False
        source = None
        
        for keyword in value_node.keywords:
            if keyword.arg == 'many':
                is_many = self._get_bool_value(keyword.value)
            elif keyword.arg == 'source':
                source = self._get_name(keyword.value)
        
        # Check if this is a relation field
        is_relation = (
            'Serializer' in func_name or
            func_name in self.serializers or
            any(rt in func_name for rt in relation_types) or
            (source and '.' in source)  # Fields with dot notation are likely relations
        )
                
        if is_relation:
            return SerializerField(
                name=field_name,
                field_type=func_name,
                is_relation=is_relation,
                is_many=is_many,
                source=source,
                line_number=getattr(value_node, 'lineno', 0),
                depth=depth
            )
            
        return None
        
    def _analyze_serializer_method_field(self, node: ast.FunctionDef) -> None:
        """Analyze SerializerMethodField getter methods"""
        field_name = node.name[4:]  # Remove 'get_' prefix
        
        # Check for relation access in method
        class RelationChecker(ast.NodeVisitor):
            def __init__(self):
                self.has_relation = False
                self.is_many = False
                self.accessed_fields = []
                
            def visit_Attribute(self, node):
                if isinstance(node.value, ast.Name) and node.value.id == 'obj':
                    self.has_relation = True
                    self.accessed_fields.append(node.attr)
                    
                if node.attr in ['all', 'filter', 'exclude']:
                    self.is_many = True
                    
                self.generic_visit(node)
                
        checker = RelationChecker()
        checker.visit(node)
        
        if checker.has_relation:
            field = SerializerField(
                name=field_name,
                field_type='SerializerMethodField',
                is_relation=True,
                is_many=checker.is_many,
                line_number=node.lineno
            )
            
            if self.current_class and self.current_class in self.serializers:
                self.serializers[self.current_class].append(field)
                
    # Model analysis
    
    def _analyze_model(self, node: ast.ClassDef) -> None:
        """Analyze Django Model class"""
        fields = []
        
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        field = self._analyze_model_field(target.id, item.value)
                        if field:
                            fields.append(field)
                            
        self.models[node.name] = fields
        
    def _analyze_model_field(
        self, field_name: str, value_node: ast.AST
    ) -> Optional[ModelField]:
        """Analyze individual model field"""
        if not isinstance(value_node, ast.Call):
            return None
            
        field_type = self._get_name(value_node.func)
        
        # Check for relation fields
        relation_mapping = {
            'ForeignKey': ('foreign_key', False),
            'OneToOneField': ('one_to_one', False),
            'ManyToManyField': ('many_to_many', True),
        }
        
        for rel_type, (rel_name, is_many) in relation_mapping.items():
            if rel_type in field_type:
                # Extract related model
                related_model = None
                if value_node.args:
                    related_model = self._get_name(value_node.args[0])
                    
                # Extract related_name
                related_name = None
                for keyword in value_node.keywords:
                    if keyword.arg == 'related_name':
                        related_name = self._get_name(keyword.value)
                        
                return ModelField(
                    name=field_name,
                    field_type=field_type,
                    is_relation=True,
                    related_model=related_model,
                    related_name=related_name,
                    is_many_to_many=rel_type == 'ManyToManyField',
                    is_foreign_key=rel_type == 'ForeignKey',
                    is_one_to_one=rel_type == 'OneToOneField',
                    line_number=getattr(value_node, 'lineno', 0)
                )
                
        return None
        
    # ViewSet analysis
    
    def _analyze_viewset(self, node: ast.ClassDef) -> None:
        """Analyze ViewSet class"""
        queryset_usages = []
        
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        if target.id == 'queryset':
                            usage = self._analyze_queryset_expression(item.value)
                            if usage:
                                usage.view_name = node.name
                                usage.line_number = item.lineno
                                queryset_usages.append(usage)
                        elif target.id == 'serializer_class':
                            serializer_name = self._get_name(item.value)
                            self.serializer_usage.append(SerializerUsage(
                                view_name=node.name,
                                serializer_name=serializer_name,
                                usage_type='class_attr',
                                line_number=item.lineno
                            ))
                            
        if queryset_usages:
            self.viewsets[node.name] = queryset_usages
            
    def _analyze_queryset_expression(self, node: ast.AST) -> Optional[QuerySetUsage]:
        """Analyze a queryset expression"""
        usage = QuerySetUsage(
            model_name="",
            view_name="",
            file_path=self.current_file
        )
        
        # Recursively analyze chained calls
        self._analyze_queryset_chain(node, usage)
        
        return usage if usage.model_name else None
        
    def _analyze_queryset_chain(self, node: ast.AST, usage: QuerySetUsage) -> None:
        """Recursively analyze QuerySet method chains"""
        if isinstance(node, ast.Call):
            func_name = self._get_name(node.func)
            
            # Extract model from .objects.all() pattern
            if '.objects.' in func_name:
                model_part = func_name.split('.objects.')[0]
                if model_part:
                    usage.model_name = model_part.split('.')[-1]
            
            # Check method name
            if isinstance(node.func, ast.Attribute):
                method = node.func.attr
                
                if method == 'select_related':
                    usage.has_select_related = True
                    self._extract_string_args(node, usage.select_related_fields)
                elif method == 'prefetch_related':
                    usage.has_prefetch_related = True
                    self._extract_prefetch_args(node, usage)
                elif method == 'annotate':
                    self._extract_annotation_names(node, usage.annotations)
                elif method == 'only':
                    self._extract_string_args(node, usage.only_fields)
                elif method == 'defer':
                    self._extract_string_args(node, usage.defer_fields)
                    
                # Continue with chain
                self._analyze_queryset_chain(node.func.value, usage)
                
    def _extract_string_args(self, node: ast.Call, field_set: Set[str]) -> None:
        """Extract string arguments from a call"""
        for arg in node.args:
            if isinstance(arg, (ast.Constant, ast.Str)):
                value = arg.value if isinstance(arg, ast.Constant) else arg.s
                field_set.add(value)
                
    def _extract_prefetch_args(self, node: ast.Call, usage: QuerySetUsage) -> None:
        """Extract prefetch_related arguments"""
        for arg in node.args:
            if isinstance(arg, (ast.Constant, ast.Str)):
                value = arg.value if isinstance(arg, ast.Constant) else arg.s
                usage.prefetch_related_fields.add(value)
            elif isinstance(arg, ast.Call):
                # Check for Prefetch objects
                func_name = self._get_name(arg.func)
                if 'Prefetch' in func_name:
                    prefetch_obj = self._analyze_prefetch_object(arg)
                    if prefetch_obj:
                        usage.prefetch_objects.append(prefetch_obj)
                        
    def _analyze_prefetch_object(self, node: ast.Call) -> Optional[Dict[str, Any]]:
        """Analyze a Prefetch() object"""
        prefetch_info = {}
        
        # Get lookup from first argument
        if node.args:
            if isinstance(node.args[0], (ast.Constant, ast.Str)):
                prefetch_info['lookup'] = (
                    node.args[0].value if isinstance(node.args[0], ast.Constant)
                    else node.args[0].s
                )
                
        # Get queryset and to_attr from keywords
        for keyword in node.keywords:
            if keyword.arg == 'queryset':
                prefetch_info['queryset'] = self._get_name(keyword.value)
            elif keyword.arg == 'to_attr':
                prefetch_info['to_attr'] = self._get_name(keyword.value)
                
        return prefetch_info if 'lookup' in prefetch_info else None
        
    def _extract_annotation_names(self, node: ast.Call, annotation_set: Set[str]) -> None:
        """Extract annotation names from annotate() call"""
        for keyword in node.keywords:
            if keyword.arg:
                annotation_set.add(keyword.arg)
                
    def _analyze_get_queryset(self, node: ast.FunctionDef) -> None:
        """Analyze get_queryset method"""
        usage = QuerySetUsage(
            model_name="",
            view_name=self.current_class or "",
            method_name="get_queryset",
            file_path=self.current_file,
            line_number=node.lineno
        )
        
        # Find all queryset operations in the method
        for child in ast.walk(node):
            if isinstance(child, ast.Return) and child.value:
                self._analyze_queryset_chain(child.value, usage)
                
        if self.current_class:
            if self.current_class not in self.viewsets:
                self.viewsets[self.current_class] = []
            self.viewsets[self.current_class].append(usage)
            
    def _analyze_get_serializer_class(self, node: ast.FunctionDef) -> None:
        """Analyze get_serializer_class method"""
        for child in ast.walk(node):
            if isinstance(child, ast.Return) and child.value:
                serializer_name = self._get_name(child.value)
                if serializer_name and self.current_class:
                    self.serializer_usage.append(SerializerUsage(
                        view_name=self.current_class,
                        serializer_name=serializer_name,
                        usage_type='get_serializer_class',
                        line_number=child.lineno
                    ))
                    
    def _analyze_action_method(self, node: ast.FunctionDef) -> None:
        """Analyze ViewSet action methods"""
        # Look for serializer usage in actions
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                func_name = self._get_name(child.func)
                if 'get_serializer' in func_name or 'Serializer' in func_name:
                    # Try to extract serializer class
                    for arg in child.args:
                        serializer_name = self._get_name(arg)
                        if serializer_name and self.current_class:
                            self.serializer_usage.append(SerializerUsage(
                                view_name=self.current_class,
                                serializer_name=serializer_name,
                                usage_type='action',
                                action_name=node.name,
                                line_number=child.lineno
                            ))
