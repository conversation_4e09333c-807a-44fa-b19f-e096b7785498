[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "django-n1-guard"
version = "0.1.0"
description = "Static analyzer to detect N+1 queries in Django/DRF projects"
readme = "README.md"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: Software Development :: Testing",
    "Framework :: Django",
    "Framework :: Django :: 3.2",
    "Framework :: Django :: 4.0",
    "Framework :: Django :: 4.1",
    "Framework :: Django :: 4.2",
    "Framework :: Django :: 5.0",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
keywords = [
    "django",
    "drf",
    "django-rest-framework",
    "n+1",
    "query",
    "optimization",
    "static-analysis",
    "linter",
    "performance"
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "colorama>=0.4.0",
    "pyyaml>=5.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
    "flake8>=5.0.0",
    "pre-commit>=3.0.0",
    "tox>=4.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "django>=3.2",
    "djangorestframework>=3.12.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/django-n1-guard"
Documentation = "https://django-n1-guard.readthedocs.io"
Repository = "https://github.com/yourusername/django-n1-guard"
Issues = "https://github.com/yourusername/django-n1-guard/issues"

[project.scripts]
django-n1-guard = "django_n1_guard.cli:main"
n1guard = "django_n1_guard.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
django_n1_guard = ["py.typed"]

[tool.black]
line-length = 100
target-version = ["py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_any_unimported = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
check_untyped_defs = true
strict_optional = true

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
addopts = [
    "--cov=django_n1_guard",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "-v"
]

[tool.coverage.run]
source = ["src/django_n1_guard"]
omit = ["*/tests/*", "*/test_*.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
