# Django N+1 Guard 🛡️

[![PyPI version](https://badge.fury.io/py/django-n1-guard.svg)](https://badge.fury.io/py/django-n1-guard)
[![Python Support](https://img.shields.io/pypi/pyversions/django-n1-guard.svg)](https://pypi.org/project/django-n1-guard/)
[![Django Support](https://img.shields.io/badge/Django-3.2%2B-green.svg)](https://djangoproject.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A powerful static analyzer to detect N+1 query issues in Django and Django REST Framework projects using AST analysis.

## 🎯 Features

- **🔍 Static Analysis**: Uses Python AST parsing for reliable, compiler-like analysis
- **🎨 DRF Support**: Comprehensive Django REST Framework serializer analysis
- **📊 Multiple Formats**: Output in text, JSON, GitHub Actions, and SARIF formats
- **⚡ Fast**: Analyzes large codebases quickly without running code
- **🎛️ Configurable**: Flexible configuration options and filtering
- **🔧 CI/CD Ready**: Perfect for pre-commit hooks and continuous integration
- **📈 Detailed Reports**: Confidence scores, severity levels, and optimization suggestions

## 🚀 Quick Start

### Installation

```bash
pip install django-n1-guard
```

### Basic Usage

```bash
# Analyze current directory
django-n1-guard .

# Analyze specific file
django-n1-guard myapp/serializers.py

# Output JSON format
django-n1-guard . --format json

# For CI/CD with exit code on issues
django-n1-guard . --fail-on-issues
```

## 📖 How It Works

Django N+1 Guard analyzes your code to find patterns like this:

### ❌ Problematic Code

```python
# serializers.py
class BookSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.name')  # N+1 risk!
    reviews = ReviewSerializer(many=True)                      # N+1 risk!
    
    class Meta:
        model = Book
        fields = ['title', 'author_name', 'reviews']

# views.py  
class BookViewSet(viewsets.ModelViewSet):
    serializer_class = BookSerializer
    queryset = Book.objects.all()  # Missing optimizations!
```

### ✅ Optimized Code

```python
# views.py
class BookViewSet(viewsets.ModelViewSet):
    serializer_class = BookSerializer
    
    def get_queryset(self):
        return Book.objects.select_related('author') \
                          .prefetch_related('reviews')
```

### 🔍 Detection Output

```
🚨 N+1 Query Issues Detected:

📁 myapp/serializers.py
  ⚠️  Line 3: BookViewSet
     Serializer: BookSerializer
     Field: author_name
     Missing: select_related
     Suggestion: .select_related('author')

  ⚠️  Line 4: BookViewSet  
     Serializer: BookSerializer
     Field: reviews
     Missing: prefetch_related
     Suggestion: .prefetch_related('reviews')

📊 Summary:
  Total issues: 2
  Warnings: 2
  Select-related issues: 1
  Prefetch-related issues: 1
```

## 🎛️ Configuration

Create a `n1guard.yml` configuration file:

```yaml
exclude:
  - migrations
  - tests
  - __pycache__

min_confidence: 0.7
severity: [warning, error]

rules:
  require_select_related: true
  require_prefetch_related: true 
  check_serializer_method_fields: true
```

## 📊 Output Formats

### Text (Default)
Human-readable colored output with suggestions.

### JSON
Machine-readable format for integration:

```bash
django-n1-guard . --format json
```

### GitHub Actions
For GitHub workflow annotations:

```bash
django-n1-guard . --format github
```

### SARIF
For integration with security tools:

```bash
django-n1-guard . --format sarif
```

## 🔧 CLI Options

```bash
django-n1-guard [PATH] [OPTIONS]

Options:
  --format [text|json|github|sarif]  Output format
  --exclude PATTERN                  Exclude patterns (multiple allowed)
  --fail-on-issues                   Exit with error code if issues found
  --min-confidence FLOAT             Minimum confidence threshold (0.0-1.0)
  --severity [info|warning|error]    Filter by severity levels
  --config FILE                      Configuration file path
  --stats                            Show analysis statistics
  --quiet                            Suppress non-essential output
  --verbose                          Enable verbose output
  --help                             Show help message
```

## 🔗 Integration

### Pre-commit Hook

Add to `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: local
    hooks:
      - id: django-n1-guard
        name: Django N+1 Guard
        entry: django-n1-guard
        language: system
        types: [python]
        args: [--fail-on-issues]
```

### GitHub Actions

```yaml
name: Code Quality

on: [push, pull_request]

jobs:
  n1-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - run: pip install django-n1-guard
      - run: django-n1-guard . --format github --fail-on-issues
```

### Django Management Command

```python
# management/commands/check_n1.py
from django.core.management.base import BaseCommand
from django_n1_guard import N1Detector

class Command(BaseCommand):
    def handle(self, *args, **options):
        detector = N1Detector()
        result = detector.analyze_directory(Path('.'))
        
        if result.issues:
            self.stdout.write(f"Found {len(result.issues)} N+1 issues")
```

## 🎯 Detection Capabilities

### Serializer Analysis
- `ModelSerializer` with relation fields
- Nested serializers
- `SerializerMethodField` with relation access
- `PrimaryKeyRelatedField`, `StringRelatedField`, etc.
- `many=True` relationships

### ViewSet/View Analysis
- `queryset` attribute analysis
- `get_queryset()` method analysis
- `select_related()` and `prefetch_related()` detection
- Custom `Prefetch` objects
- Method decorators (`@action`)

### Advanced Features
- Cross-file analysis
- Confidence scoring
- False positive reduction
- Complex field path detection (`source='author.name'`)
- Inheritance chain analysis

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md).

### Development Setup

```bash
git clone https://github.com/yourusername/django-n1-guard
cd django-n1-guard
pip install -e ".[dev]"
pre-commit install
```

### Running Tests

```bash
pytest
pytest --cov=django_n1_guard
```

## 🐛 Limitations

- Static analysis only (no runtime detection)
- May not catch all dynamic QuerySet construction
- Complex inheritance patterns might be missed
- SerializerMethodField analysis is heuristic-based

## 📄 License

MIT License - see [LICENSE](LICENSE) file.

## 🙏 Acknowledgments

- Inspired by Django's built-in system checks
- Built with Python's powerful AST module
- Thanks to the Django and DRF communities

## 📚 Related Projects

- [django-query-inspector](https://github.com/junqed/django-query-inspector) - Runtime N+1 detection
- [django-silk](https://github.com/jazzband/django-silk) - Performance profiling
- [nplusone](https://github.com/jmcarp/nplusone) - Runtime detection for multiple ORMs

---

**Made with ❤️ for the Django community**